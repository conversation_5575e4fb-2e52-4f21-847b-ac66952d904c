#!/bin/bash

API_BASE="http://localhost:5000/api"

echo "🧪 Testing Sticky Note Kanban API..."
echo

# Test 1: Health check
echo "1. Testing health endpoint..."
curl -s -X GET "$API_BASE/health" | echo "✅ Health check response received"
echo

# Test 2: Get all notes
echo "2. Getting all notes..."
curl -s -X GET "$API_BASE/notes" | head -100
echo
echo "✅ Notes endpoint accessible"
echo

# Test 3: Create a test note
echo "3. Creating a test note..."
NOTE_DATA='{
  "content": "Test sticky note from Docker setup! 🎉",
  "color": "#FFEB9C",
  "assigner": "Docker Test",
  "status": "todo"
}'

CREATE_RESULT=$(curl -s -X POST "$API_BASE/notes" \
  -H "Content-Type: application/json" \
  -d "$NOTE_DATA")

echo "$CREATE_RESULT" | head -200
echo
echo "✅ Note creation endpoint tested"
echo

echo "🎉 Basic API tests completed! Check the responses above."
echo "📋 Frontend available at: http://localhost:5173"
echo "🔧 Backend API available at: http://localhost:5000"