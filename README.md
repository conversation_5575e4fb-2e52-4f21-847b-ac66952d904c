# Sticky Note Kanban Application - <PERSON><PERSON> Hoạch Thiết Kế

## Tổng Quan

Ứng dụng Sticky Note Kanban là một công cụ quản lý công việc trực quan, cho phép người dùng tạo, chỉnh sửa và di chuyển các ghi chú qua ba trạng thái: Todo, Processing, và Done. Ứng dụng sử dụng giao diện kéo-thả trực quan với thiết kế giống bảng gỗ thật.

---

## Phần 1: Thiết Kế Backend

### 1.1. Mô Hình Schema

#### SQLite Schema (Lightweight Database)
```javascript
const StickyNoteSchema = {
  _id: ObjectId,
  content: {
    type: String,
    required: true,
    maxlength: 500
  },
  color: {
    type: String,
    default: "#FFEB9C", // Màu vàng mặc định
    match: /^#[0-9A-Fa-f]{6}$/
  },
  status: {
    type: String,
    enum: ["todo", "processing", "done"],
    default: "todo"
  },
  order: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  assigner: {
    type: String,
    required: false,
    maxlength: 100
  },
  deadline: {
    type: Date,
    required: false
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}
```

#### PostgreSQL Schema (SQL)
```sql
CREATE TABLE sticky_notes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  content TEXT NOT NULL CHECK (length(content) <= 500),
  color VARCHAR(7) DEFAULT '#FFEB9C' CHECK (color ~ '^#[0-9A-Fa-f]{6}$'),
  status VARCHAR(20) DEFAULT 'todo' CHECK (status IN ('todo', 'processing', 'done')),
  order_position INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  assigner VARCHAR(100),
  deadline TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_sticky_notes_status ON sticky_notes(status);
CREATE INDEX idx_sticky_notes_order ON sticky_notes(status, order_position);
```

### 1.2. API Endpoints Chi Tiết

#### GET /api/notes
- **Mục đích**: Lấy toàn bộ danh sách ghi chú, sắp xếp theo status và order
- **Phương thức**: GET
- **Query Parameters**:
  - `status` (optional): Lọc theo trạng thái cụ thể
  - `sortBy` (optional): Sắp xếp theo trường (default: order)

**Response Example**:
```json
{
  "success": true,
  "data": [
    {
      "id": "64a7b1c2e1234567890abcde",
      "content": "Hoàn thành báo cáo tháng",
      "color": "#FFE4E1",
      "status": "todo",
      "order": 1,
      "createdAt": "2023-07-06T10:30:00.000Z",
      "assigner": "Nguyễn Văn A",
      "deadline": "2023-07-15T17:00:00.000Z",
      "updatedAt": "2023-07-06T10:30:00.000Z"
    }
  ],
  "count": 1
}
```

#### POST /api/notes
- **Mục đích**: Tạo ghi chú mới
- **Phương thức**: POST

**Request Body**:
```json
{
  "content": "Nội dung ghi chú",
  "color": "#FFEB9C",
  "status": "todo",
  "assigner": "Tên người giao",
  "deadline": "2023-07-15T17:00:00.000Z"
}
```

**Response Example**:
```json
{
  "success": true,
  "data": {
    "id": "64a7b1c2e1234567890abcde",
    "content": "Nội dung ghi chú",
    "color": "#FFEB9C",
    "status": "todo",
    "order": 0,
    "createdAt": "2023-07-06T10:30:00.000Z",
    "assigner": "Tên người giao",
    "deadline": "2023-07-15T17:00:00.000Z",
    "updatedAt": "2023-07-06T10:30:00.000Z"
  }
}
```

#### PUT /api/notes/{id}
- **Mục đích**: Cập nhật thông tin ghi chú
- **Phương thức**: PUT

**Request Body**:
```json
{
  "content": "Nội dung đã chỉnh sửa",
  "color": "#E6E6FA",
  "status": "processing",
  "order": 2,
  "assigner": "Người giao mới",
  "deadline": "2023-07-20T17:00:00.000Z"
}
```

#### PUT /api/notes/reorder
- **Mục đích**: Cập nhật thứ tự nhiều ghi chú cùng lúc (khi kéo-thả)
- **Phương thức**: PUT

**Request Body**:
```json
{
  "updates": [
    {
      "id": "64a7b1c2e1234567890abcde",
      "status": "processing",
      "order": 1
    },
    {
      "id": "64a7b1c2e1234567890abcdf",
      "order": 2
    }
  ]
}
```

#### DELETE /api/notes/{id}
- **Mục đích**: Xóa ghi chú
- **Phương thức**: DELETE

**Response Example**:
```json
{
  "success": true,
  "message": "Ghi chú đã được xóa thành công"
}
```

---

## Phần 2: Thiết Kế Frontend

### 2.1. Cấu Trúc Component

#### Board Component
- **Chức năng**: Component chính quản lý toàn bộ bảng Kanban
- **State**: 
  - `notes`: Danh sách tất cả ghi chú
  - `loading`: Trạng thái tải dữ liệu
  - `error`: Thông báo lỗi
- **Props**: Không có (component gốc)

```jsx
const Board = () => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Xử lý drag and drop
  const handleDragEnd = (result) => {
    // Logic cập nhật vị trí và status
  };

  return (
    <div className="board">
      <Column status="todo" notes={todoNotes} />
      <Column status="processing" notes={processingNotes} />
      <Column status="done" notes={doneNotes} />
    </div>
  );
};
```

#### Column Component
- **Chức năng**: Hiển thị một cột trạng thái với counter và ghi chú
- **State**:
  - `collapsed`: Trạng thái gom/mở rộng (chỉ cho cột Done)
- **Props**: 
  - `status`: Trạng thái của cột
  - `notes`: Danh sách ghi chú trong cột
  - `onDrop`: Callback khi thả ghi chú

```jsx
const Column = ({ status, notes, onDrop }) => {
  const [collapsed, setCollapsed] = useState(false);
  
  const titles = {
    todo: "Todo",
    processing: "Processing", 
    done: "Done"
  };

  return (
    <div className="column">
      <div className="column-header">
        <h3>{titles[status]}</h3>
        <span className="counter">({notes.length})</span>
        {status === 'done' && (
          <button onClick={() => setCollapsed(!collapsed)}>
            {collapsed ? 'Expand' : 'Collapse'}
          </button>
        )}
      </div>
      <div className={`column-content ${collapsed ? 'collapsed' : ''}`}>
        {notes.map(note => (
          <StickyNote key={note.id} note={note} />
        ))}
      </div>
    </div>
  );
};
```

#### StickyNote Component
- **Chức năng**: Hiển thị một ghi chú với thiết kế giấy dán
- **State**: Không có (pure component)
- **Props**:
  - `note`: Dữ liệu ghi chú
  - `onEdit`: Callback khi chỉnh sửa
  - `onDelete`: Callback khi xóa

```jsx
const StickyNote = ({ note, onEdit, onDelete }) => {
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('vi-VN');
  };

  return (
    <div 
      className="sticky-note"
      style={{ backgroundColor: note.color }}
      draggable
    >
      <div className="pin"></div>
      <div className="content">{note.content}</div>
      <div className="metadata">
        <div className="created-date">
          Tạo: {formatDate(note.createdAt)}
        </div>
        {note.assigner && (
          <div className="assigner">
            Giao: {note.assigner}
          </div>
        )}
        {note.deadline && (
          <div className="deadline">
            Hạn: {formatDate(note.deadline)}
          </div>
        )}
      </div>
      <div className="actions">
        <button onClick={() => onEdit(note)}>Sửa</button>
        <button onClick={() => onDelete(note.id)}>Xóa</button>
      </div>
    </div>
  );
};
```

#### NoteEditorModal Component
- **Chức năng**: Modal để tạo/chỉnh sửa ghi chú
- **State**:
  - `formData`: Dữ liệu form
  - `errors`: Validation errors
- **Props**:
  - `isOpen`: Trạng thái hiển thị modal
  - `note`: Ghi chú cần sửa (null khi tạo mới)
  - `onSave`: Callback khi lưu
  - `onClose`: Callback khi đóng

```jsx
const NoteEditorModal = ({ isOpen, note, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    content: '',
    color: '#FFEB9C',
    assigner: '',
    deadline: ''
  });

  const colorOptions = [
    '#FFEB9C', '#FFE4E1', '#E6E6FA', '#F0FFF0', 
    '#F5F5DC', '#FDF5E6', '#E0FFFF', '#FFF8DC'
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label>Nội dung*</label>
            <textarea 
              value={formData.content}
              onChange={(e) => setFormData({...formData, content: e.target.value})}
              required
              maxLength={500}
            />
          </div>
          
          <div className="form-group">
            <label>Màu sắc</label>
            <div className="color-picker">
              {colorOptions.map(color => (
                <button
                  key={color}
                  type="button"
                  className={`color-option ${formData.color === color ? 'selected' : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => setFormData({...formData, color})}
                />
              ))}
            </div>
          </div>

          <div className="form-group">
            <label>Người giao</label>
            <input 
              type="text"
              value={formData.assigner}
              onChange={(e) => setFormData({...formData, assigner: e.target.value})}
              maxLength={100}
            />
          </div>

          <div className="form-group">
            <label>Hạn chót</label>
            <input 
              type="datetime-local"
              value={formData.deadline}
              onChange={(e) => setFormData({...formData, deadline: e.target.value})}
            />
          </div>

          <div className="form-actions">
            <button type="button" onClick={onClose}>Hủy</button>
            <button type="submit">Lưu</button>
          </div>
        </form>
      </div>
    </div>
  );
};
```

### 2.2. Luồng Dữ Liệu và Tương Tác API

#### State Management Flow
```
Board Component (Root State)
├── notes: StickyNote[]
├── loading: boolean
└── error: string | null

API Integration:
1. Component mount → fetchNotes() → GET /api/notes
2. Create note → POST /api/notes → update local state
3. Update note → PUT /api/notes/{id} → update local state  
4. Delete note → DELETE /api/notes/{id} → remove from local state
5. Drag & Drop → PUT /api/notes/reorder → update multiple notes
```

#### Drag and Drop Logic
```jsx
// Trong Board component
const handleDragEnd = async (result) => {
  const { source, destination, draggableId } = result;
  
  if (!destination) return;
  
  const updates = [];
  const draggedNote = notes.find(n => n.id === draggableId);
  
  // Case 1: Di chuyển giữa các cột
  if (source.droppableId !== destination.droppableId) {
    updates.push({
      id: draggableId,
      status: destination.droppableId,
      order: destination.index
    });
    
    // Cập nhật order các notes khác trong cột đích
    const destinationNotes = notes
      .filter(n => n.status === destination.droppableId && n.id !== draggableId)
      .sort((a, b) => a.order - b.order);
    
    destinationNotes.forEach((note, index) => {
      const newOrder = index >= destination.index ? index + 1 : index;
      if (newOrder !== note.order) {
        updates.push({ id: note.id, order: newOrder });
      }
    });
  }
  
  // Case 2: Sắp xếp trong cùng một cột
  else if (source.index !== destination.index) {
    const columnNotes = notes
      .filter(n => n.status === source.droppableId)
      .sort((a, b) => a.order - b.order);
    
    // Tính toán lại order cho tất cả notes trong cột
    columnNotes.forEach((note, index) => {
      let newOrder = index;
      if (note.id === draggableId) {
        newOrder = destination.index;
      } else if (index >= Math.min(source.index, destination.index) && 
                 index <= Math.max(source.index, destination.index)) {
        newOrder = source.index < destination.index ? index - 1 : index + 1;
      }
      
      if (newOrder !== note.order) {
        updates.push({ id: note.id, order: newOrder });
      }
    });
  }
  
  if (updates.length > 0) {
    try {
      await fetch('/api/notes/reorder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates })
      });
      
      // Cập nhật local state
      setNotes(prevNotes => 
        prevNotes.map(note => {
          const update = updates.find(u => u.id === note.id);
          return update ? { ...note, ...update } : note;
        })
      );
    } catch (error) {
      console.error('Failed to update note order:', error);
      // Có thể thêm toast notification hoặc revert changes
    }
  }
};
```

---

## Phần 3: Đề Xuất Công Nghệ (Technology Stack)

### 3.1. Backend Technologies

#### Option 1: Node.js + Express (Recommended)
```javascript
// Lý do chọn:
// - JavaScript end-to-end
// - Ecosystem phong phú 
// - Dễ deploy và scale
// - Tích hợp tốt với MongoDB

const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');

// Dependencies:
// - express: Web framework
// - mongoose: MongoDB ODM
// - cors: Cross-origin requests
// - joi: Validation
// - helmet: Security
// - rate-limiter: API protection
```

#### Option 2: Python + FastAPI
```python
# Lý do chọn:
# - Performance cao
# - Auto documentation
# - Type hints
# - Async support

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from motor.motor_asyncio import AsyncIOMotorClient

# Dependencies:
# - fastapi: Modern web framework
# - uvicorn: ASGI server
# - motor: Async MongoDB driver
# - pydantic: Data validation
```

### 3.2. Database Options

#### MongoDB (Recommended)
```javascript
// Pros:
// - Schema flexibility
// - JSON-like documents
// - Easy to scale horizontally
// - Good for rapid development

// Cons:
// - No ACID transactions (across documents)
// - Larger memory footprint
```

#### PostgreSQL
```sql
-- Pros:
-- - ACID compliance
-- - Strong consistency
-- - Advanced querying
-- - JSON support

-- Cons:
-- - More complex setup
-- - Vertical scaling limitations
```

### 3.3. Frontend Technologies

#### React + TypeScript (Recommended)
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "@dnd-kit/core": "^6.0.8",
    "@dnd-kit/sortable": "^7.0.2",
    "@dnd-kit/utilities": "^3.2.1",
    "react-query": "^3.39.3",
    "axios": "^1.4.0",
    "styled-components": "^6.0.0",
    "react-hook-form": "^7.45.0",
    "react-hot-toast": "^2.4.1"
  }
}
```

#### Lý do chọn thư viện:

**@dnd-kit** thay vì react-beautiful-dnd:
- Accessibility tốt hơn
- Touch device support
- Modular architecture
- Active maintenance
- TypeScript first

**React Query** cho data fetching:
- Caching tự động
- Background refetching
- Optimistic updates
- Error handling

**Styled Components** cho styling:
- CSS-in-JS
- Theme support
- Dynamic styling
- Component isolation

### 3.4. Development Tools

```json
{
  "devDependencies": {
    "vite": "^4.4.0",
    "eslint": "^8.45.0",
    "prettier": "^3.0.0",
    "jest": "^29.6.0",
    "@testing-library/react": "^13.4.0",
    "cypress": "^12.17.0",
    "husky": "^8.0.3",
    "lint-staged": "^13.2.3"
  }
}
```

---

## Phần 4: Luồng Người Dùng (User Flow)

### 4.1. User Flow Chính

```
1. Khởi động ứng dụng
   │
   ├─→ Loading state hiển thị
   │
   ├─→ Gọi API GET /api/notes
   │
   ├─→ Render 3 cột: Todo, Processing, Done
   │
   └─→ Hiển thị counter trên mỗi cột

2. Tạo ghi chú mới
   │
   ├─→ Click nút "Thêm ghi chú"
   │
   ├─→ Modal NoteEditorModal mở
   │
   ├─→ Nhập nội dung (bắt buộc)
   │
   ├─→ Chọn màu từ color picker
   │
   ├─→ Điền thông tin tùy chọn (assigner, deadline)
   │
   ├─→ Click "Lưu" → POST /api/notes
   │
   ├─→ Ghi chú mới xuất hiện ở cột Todo
   │
   └─→ Counter cột Todo tăng +1

3. Di chuyển ghi chú (Drag & Drop)
   │
   ├─→ Hover vào ghi chú → cursor thay đổi
   │
   ├─→ Drag ghi chú từ cột này sang cột khác
   │
   ├─→ Drop area highlight khi drag over
   │
   ├─→ Thả ghi chú → onDragEnd trigger
   │
   ├─→ Gọi API PUT /api/notes/reorder
   │
   ├─→ Cập nhật UI immediately (optimistic update)
   │
   └─→ Counter các cột cập nhật

4. Sắp xếp trong cùng một cột
   │
   ├─→ Drag ghi chú lên/xuống trong cột
   │
   ├─→ Visual feedback về vị trí sẽ drop
   │
   ├─→ Drop → cập nhật order của ghi chú
   │
   └─→ API call cập nhật thứ tự

5. Chỉnh sửa ghi chú
   │
   ├─→ Click vào ghi chú hoặc nút "Sửa"
   │
   ├─→ Modal mở với dữ liệu hiện tại
   │
   ├─→ Chỉnh sửa các trường cần thiết
   │
   ├─→ Click "Lưu" → PUT /api/notes/{id}
   │
   └─→ Ghi chú cập nhật trên UI

6. Xóa ghi chú
   │
   ├─→ Click nút "Xóa" trên ghi chú
   │
   ├─→ Confirmation dialog xuất hiện
   │
   ├─→ Xác nhận → DELETE /api/notes/{id}
   │
   ├─→ Ghi chú biến mất từ UI
   │
   └─→ Counter giảm -1

7. Quản lý cột Done
   │
   ├─→ Khi có nhiều ghi chú Done
   │
   ├─→ Click nút "Collapse" trên header
   │
   ├─→ Tất cả ghi chú gom thành một khối
   │
   ├─→ Click khối hoặc "Expand"
   │
   └─→ Hiển thị lại toàn bộ danh sách
```

### 4.2. Error Handling Flow

```
1. Network Error
   │
   ├─→ Show toast notification "Lỗi kết nối"
   │
   ├─→ Retry mechanism tự động (3 lần)
   │
   └─→ Manual retry button nếu thất bại

2. Validation Error
   │
   ├─→ Form validation real-time
   │
   ├─→ Error messages hiển thị dưới fields
   │
   └─→ Submit button disabled until valid

3. Server Error (5xx)
   │
   ├─→ Generic error message
   │
   ├─→ Log error to monitoring service
   │
   └─→ Fallback UI state

4. Drag & Drop Error
   │
   ├─→ Revert to previous position
   │
   ├─→ Show error notification
   │
   └─→ Allow user to retry
```

### 4.3. Performance Optimizations

```
1. Data Loading
   │
   ├─→ Initial load: Fetch all notes
   │
   ├─→ Background sync every 30s
   │
   ├─→ Optimistic updates for better UX
   │
   └─→ Cache responses với React Query

2. Rendering Performance
   │
   ├─→ Virtual scrolling cho cột có nhiều notes
   │
   ├─→ React.memo cho StickyNote component
   │
   ├─→ Debounced search/filter
   │
   └─→ Lazy load images/avatars

3. Bundle Optimization
   │
   ├─→ Code splitting by routes
   │
   ├─→ Tree shaking unused code
   │
   ├─→ Compress images và assets
   │
   └─→ Service worker cho offline support
```

---

## Kết Luận

Kế hoạch này cung cấp một roadmap đầy đủ để xây dựng ứng dụng Sticky Note Kanban với đầy đủ chức năng được yêu cầu. Architecture được thiết kế để đảm bảo:

- **Scalability**: Có thể mở rộng số lượng người dùng và dữ liệu
- **Maintainability**: Code dễ bảo trì và phát triển thêm
- **User Experience**: Giao diện trực quan và responsive
- **Performance**: Tối ưu hóa tốc độ tải và tương tác
- **Reliability**: Xử lý lỗi và edge cases đầy đủ

Với technology stack được đề xuất, team có thể bắt đầu development ngay lập tức và delivery một MVP trong 4-6 tuần.