version: "3.8"

services:
  # Backend API - Now using SQLite
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: sticky-note-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
    ports:
      - "3001:3001"
    volumes:
      - ./server:/app/server
      - /app/node_modules
      - sqlite_data:/app/server/data # SQLite database storage
    networks:
      - sticky-note-network
    command: node server/index.js

  # Frontend React App
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: sticky-note-frontend
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: http://localhost:3001/api
    ports:
      - "5173:5173"
    depends_on:
      - backend
    volumes:
      - ./client/src:/app/src
      - /app/node_modules
    networks:
      - sticky-note-network
    command: npm run dev

volumes:
  sqlite_data:
    driver: local

networks:
  sticky-note-network:
    driver: bridge
