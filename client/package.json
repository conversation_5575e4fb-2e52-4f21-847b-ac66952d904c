{"name": "sticky-note-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@tanstack/react-query": "^5.59.0", "axios": "^1.7.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "styled-components": "^6.1.13"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}