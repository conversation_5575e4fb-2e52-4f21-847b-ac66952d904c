import React, { useState, memo, useMemo, useEffect } from 'react';
import styled from 'styled-components';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable';
import StickyNote from './StickyNote';
import QuickAddNote from './QuickAddNote';
import type { StickyNote as StickyNoteType, Quadrant as QuadrantType } from '../types';

const QuadrantContainer = styled.div<{ $isOver: boolean; $color: string }>`
  background: ${props => props.$isOver
    ? `linear-gradient(135deg, ${props.$color}20 0%, ${props.$color}10 100%)`
    : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'};
  border: 2px solid ${props => props.$isOver ? props.$color : '#dee2e6'};
  border-radius: 8px; /* Smaller border radius */
  padding: 12px; /* Reduced padding */
  /* Ensure uniform sizing across all quadrants */
  width: 100%;
  height: 100%;
  min-height: 220px; /* Reduced minimum height */
  max-height: 100%; /* Prevent overflow */
  display: flex;
  flex-direction: column;
  position: relative; /* For absolute positioned create button */
  transition: all 0.3s ease;
  box-shadow: ${props => props.$isOver
    ? `0 6px 20px ${props.$color}30`
    : '0 2px 8px rgba(0, 0, 0, 0.1)'};
  overflow: hidden; /* Prevent content overflow */

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    padding: 10px;
    min-height: 200px; /* Reduced on tablets */
    border-radius: 6px;
  }

  @media (max-width: 768px) {
    padding: 8px;
    min-height: 180px; /* Reduced on mobile */
    max-height: none; /* Allow natural height on mobile */
    border-radius: 6px;
  }
`;

const QuadrantHeader = styled.div`
  margin-bottom: 8px; /* Further reduced margin */
  text-align: center;
`;

const QuadrantTitle = styled.h3<{ $color: string }>`
  margin: 0 0 2px 0; /* Further reduced margin */
  font-size: 14px; /* Smaller font size */
  font-weight: 700;
  color: ${props => props.$color};
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    font-size: 12px;
  }
`;

const QuadrantSubtitle = styled.p`
  margin: 0 0 4px 0; /* Further reduced margin */
  font-size: 10px; /* Smaller font size */
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;

  @media (max-width: 768px) {
    font-size: 9px;
  }
`;

const Counter = styled.span<{ $color: string }>`
  background: ${props => props.$color};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
`;

const QuadrantContent = styled.div`
  flex: 1;
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 5 main columns for sub-blocks */
  grid-template-rows: 1fr; /* Single row containing sub-blocks */
  gap: 8px; /* Smaller gap for 5 columns */
  overflow-y: auto;
  max-height: calc(100vh - 220px);
  align-content: start;
  padding: 4px;

  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    grid-template-columns: repeat(4, 1fr); /* 4 sub-blocks on tablets */
    gap: 6px;
    max-height: calc(100vh - 200px);
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr); /* 3 sub-blocks on mobile */
    gap: 6px;
    max-height: none;
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr); /* 2 sub-blocks on small mobile */
    gap: 6px;
  }
`;

const SubBlock = styled.div<{ $isOver: boolean; $color: string; $blockId: string }>`
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 120px;
  padding: 8px;
  border-radius: 8px;
  border: 2px dashed ${props => props.$isOver ? props.$color : 'rgba(0, 0, 0, 0.1)'};
  background: ${props => props.$isOver
    ? `linear-gradient(135deg, ${props.$color}10 0%, ${props.$color}05 100%)`
    : 'rgba(255, 255, 255, 0.3)'};
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    border-color: ${props => props.$color};
    background: ${props => `linear-gradient(135deg, ${props.$color}08 0%, ${props.$color}03 100%)`};
  }

  /* Sub-block labels */
  &::before {
    content: ${props => {
      switch (props.$blockId) {
        case 'block-1': return '"Cột 1"';
        case 'block-2': return '"Cột 2"';
        case 'block-3': return '"Cột 3"';
        case 'block-4': return '"Cột 4"';
        case 'block-5': return '"Cột 5"';
        default: return '""';
      }
    }};
    position: absolute;
    top: -8px;
    left: 8px;
    background: ${props => props.$color};
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    opacity: 0.7;
  }
`;

const EmptyState = styled.div<{ $isOver: boolean; $color: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px; /* Reduced padding for horizontal layout */
  text-align: center;
  border: 2px dashed ${props => props.$isOver ? props.$color : '#dee2e6'};
  border-radius: 10px;
  background: ${props => props.$isOver
    ? `${props.$color}10`
    : 'rgba(255, 255, 255, 0.5)'};
  transition: all 0.3s ease;
  min-height: 150px; /* Reduced height for more compact layout */
  width: 100%; /* Full width in horizontal layout */
`;

const EmptyStateIcon = styled.div`
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
`;

const EmptyStateTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
`;

const EmptyStateSubtext = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6c757d;
  line-height: 1.4;
`;

const CreateButton = styled.button<{ $color: string }>`
  background: ${props => props.$color};
  color: white;
  border: none;
  border-radius: 50%; /* Circular button */
  width: 36px; /* Slightly larger for better visibility */
  height: 36px; /* Slightly larger for better visibility */
  font-size: 18px; /* Larger icon size */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 12px;
  right: 12px;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  &:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: translateY(0) scale(1.05);
  }
`;

const CapacityWarning = styled.div<{ $color: string }>`
  position: absolute;
  bottom: 12px;
  right: 12px;
  padding: 8px 12px;
  background: rgba(255, 193, 7, 0.9);
  color: #856404;
  border: 1px solid #ffc107;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  white-space: nowrap;

  @media (max-width: 768px) {
    font-size: 11px;
    padding: 6px 10px;
  }
`;

interface SubBlockProps {
  blockId: string;
  quadrant: QuadrantType;
  color: string;
  notes: StickyNoteType[];
  onNoteClick?: (note: StickyNoteType) => void;
  onNoteDelete: (id: string) => void;
  onToggleComplete?: (note: StickyNoteType) => void;
}

const SubBlockComponent: React.FC<SubBlockProps> = ({
  blockId,
  quadrant,
  color,
  notes,
  onNoteClick,
  onNoteDelete,
  onToggleComplete
}) => {
  const { isOver, setNodeRef } = useDroppable({
    id: `${quadrant}-${blockId}`,
  });

  const noteIds = useMemo(() => notes.map(note => note.id), [notes]);

  return (
    <SubBlock ref={setNodeRef} $isOver={isOver} $color={color} $blockId={blockId}>
      <SortableContext items={noteIds} strategy={rectSortingStrategy}>
        {notes.map((note) => (
          <StickyNote
            key={note.id}
            note={note}
            onEdit={onNoteClick || (() => {})}
            onDelete={onNoteDelete}
            onToggleComplete={onToggleComplete}
          />
        ))}
      </SortableContext>
    </SubBlock>
  );
};

interface QuadrantProps {
  quadrant: QuadrantType;
  title: string;
  subtitle: string;
  color: string;
  notes: StickyNoteType[];
  onNoteEdit: (note: StickyNoteType) => void; // For drag-and-drop updates
  onNoteClick?: (note: StickyNoteType) => void; // For click-to-edit functionality
  onNoteDelete: (id: string) => void;
  onCreateNote?: (quadrant: QuadrantType) => void;
  onQuickCreateNote?: (description: string, quadrant: QuadrantType) => void;
  onToggleComplete?: (note: StickyNoteType) => void;
}

const Quadrant: React.FC<QuadrantProps> = ({
  quadrant,
  title,
  subtitle,
  color,
  notes,
  onNoteEdit,
  onNoteClick,
  onNoteDelete,
  onCreateNote,
  onQuickCreateNote,
  onToggleComplete
}) => {
  // Removed showQuickAdd state - no longer using quick-add functionality
  
  const { isOver, setNodeRef } = useDroppable({
    id: quadrant,
  });

  // Sort notes by priority and order
  const sortedNotes = useMemo(() => {
    return [...notes].sort((a, b) => {
      const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
      const aPriority = priorityOrder[a.priority || 'medium'];
      const bPriority = priorityOrder[b.priority || 'medium'];
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }
      
      return a.order - b.order;
    });
  }, [notes]);

  const noteIds = useMemo(() => sortedNotes.map(note => note.id), [sortedNotes]);

  // Check if quadrant is at capacity (max 15 notes: 5 columns × 3 rows)
  const isAtCapacity = notes.length >= 15;
  const capacityPercentage = Math.round((notes.length / 15) * 100);

  const getEmptyStateContent = () => {
    const icons = {
      important_urgent: '🚨',
      important_not_urgent: '📅',
      not_important_urgent: '👥',
      not_important_not_urgent: '🗑️'
    };

    const messages = {
      important_urgent: {
        title: 'Chưa có việc gấp rút',
        subtitle: 'Đây là nơi cho những việc quan trọng và khẩn cấp cần xử lý ngay'
      },
      important_not_urgent: {
        title: 'Chưa có việc cần lên lịch',
        subtitle: 'Đây là nơi cho những việc quan trọng nhưng không khẩn cấp'
      },
      not_important_urgent: {
        title: 'Chưa có việc cần ủy thác',
        subtitle: 'Đây là nơi cho những việc khẩn cấp nhưng không quan trọng'
      },
      not_important_not_urgent: {
        title: 'Chưa có việc cần loại bỏ',
        subtitle: 'Đây là nơi cho những việc không quan trọng và không khẩn cấp'
      }
    };

    return {
      icon: icons[quadrant],
      ...messages[quadrant]
    };
  };

  // Removed handleQuickAdd - no longer using quick-add functionality

  const emptyContent = getEmptyStateContent();

  // Distribute notes across 5 sub-blocks with vertical-first arrangement
  const distributeNotes = useMemo(() => {
    const blocks = { 'block-1': [], 'block-2': [], 'block-3': [], 'block-4': [], 'block-5': [] };
    const blockKeys = Object.keys(blocks);

    // Distribute notes vertically first (column-first arrangement) and assign column indices
    sortedNotes.forEach((note, index) => {
      const blockIndex = index % 5; // Cycle through 5 blocks
      const noteWithColumn = { ...note, columnIndex: blockIndex + 1 }; // Column 1-5
      blocks[blockKeys[blockIndex]].push(noteWithColumn);
    });

    return blocks;
  }, [sortedNotes]);

  const renderContent = () => {
    if (notes.length === 0) {
      return (
        <EmptyState $isOver={isOver} $color={color}>
          {isOver ? (
            <>
              <EmptyStateIcon>🎯</EmptyStateIcon>
              <EmptyStateTitle>Thả ghi chú vào đây</EmptyStateTitle>
              <EmptyStateSubtext>Kéo một ghi chú từ ô khác để thay đổi phân loại</EmptyStateSubtext>
            </>
          ) : (
            <>
              <EmptyStateIcon>{emptyContent.icon}</EmptyStateIcon>
              <EmptyStateTitle>{emptyContent.title}</EmptyStateTitle>
              <EmptyStateSubtext>{emptyContent.subtitle}</EmptyStateSubtext>
              {onCreateNote && (
                <CreateButton $color={color} onClick={() => onCreateNote(quadrant)} title="Tạo ghi chú mới">
                  +
                </CreateButton>
              )}
            </>
          )}
        </EmptyState>
      );
    }

    return (
      <>
        <SubBlockComponent
          blockId="block-1"
          quadrant={quadrant}
          color={color}
          notes={distributeNotes['block-1']}
          onNoteClick={onNoteClick}
          onNoteDelete={onNoteDelete}
          onToggleComplete={onToggleComplete}
        />
        <SubBlockComponent
          blockId="block-2"
          quadrant={quadrant}
          color={color}
          notes={distributeNotes['block-2']}
          onNoteClick={onNoteClick}
          onNoteDelete={onNoteDelete}
          onToggleComplete={onToggleComplete}
        />
        <SubBlockComponent
          blockId="block-3"
          quadrant={quadrant}
          color={color}
          notes={distributeNotes['block-3']}
          onNoteClick={onNoteClick}
          onNoteDelete={onNoteDelete}
          onToggleComplete={onToggleComplete}
        />
        <SubBlockComponent
          blockId="block-4"
          quadrant={quadrant}
          color={color}
          notes={distributeNotes['block-4']}
          onNoteClick={onNoteClick}
          onNoteDelete={onNoteDelete}
          onToggleComplete={onToggleComplete}
        />
        <SubBlockComponent
          blockId="block-5"
          quadrant={quadrant}
          color={color}
          notes={distributeNotes['block-5']}
          onNoteClick={onNoteClick}
          onNoteDelete={onNoteDelete}
          onToggleComplete={onToggleComplete}
        />
      </>
    );
  };

  return (
    <QuadrantContainer ref={setNodeRef} $isOver={isOver} $color={color}>
      <QuadrantHeader>
        <QuadrantTitle $color={color}>
          {title}
          <Counter $color={color}>({notes.length})</Counter>
        </QuadrantTitle>
        <QuadrantSubtitle>{subtitle}</QuadrantSubtitle>
      </QuadrantHeader>

      <QuadrantContent>
        {renderContent()}
      </QuadrantContent>

      {/* Create button positioned absolutely in bottom-right corner */}
      {onCreateNote && !isAtCapacity && (
        <CreateButton $color={color} onClick={() => onCreateNote(quadrant)} title="Tạo ghi chú mới">
          +
        </CreateButton>
      )}

      {isAtCapacity && (
        <CapacityWarning $color={color}>
          ⚠️ Đã đạt giới hạn tối đa (15 ghi chú)
        </CapacityWarning>
      )}
    </QuadrantContainer>
  );
};

export default memo(Quadrant);
