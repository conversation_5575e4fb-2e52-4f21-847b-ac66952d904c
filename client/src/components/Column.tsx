import React, { useState, memo, useMemo, useEffect } from 'react';
import styled from 'styled-components';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import StickyNote from './StickyNote';
import QuickAddNote from './QuickAddNote';
import type { ColumnProps, Status } from '../types';

const ColumnContainer = styled.div<{ $isOver: boolean }>`
  height: calc(100vh - 280px);
  background: rgba(45, 27, 105, 0.9);
  border-radius: 20px;
  backdrop-filter: blur(15px);
  border: 1px solid ${props => props.$isOver
    ? 'rgba(6, 182, 212, 0.8)'
    : 'rgba(99, 102, 241, 0.3)'};
  box-shadow: ${props => props.$isOver
    ? `0 25px 60px rgba(6, 182, 212, 0.3),
       0 35px 80px rgba(0, 0, 0, 0.25),
       inset 0 1px 0 rgba(255, 255, 255, 0.1),
       0 0 40px rgba(6, 182, 212, 0.2)`
    : `0 20px 50px rgba(0, 0, 0, 0.25),
       inset 0 1px 0 rgba(255, 255, 255, 0.05)`};
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: ${props => props.$isOver ? 'scale(1.02)' : 'scale(1)'};
  display: flex;
  flex-direction: column;
  overflow: hidden;

  /* Responsive width */
  width: 350px;
  flex-shrink: 0;
  
  @media (min-width: 1200px) {
    height: calc(100vh - 300px);
  }

  @media (max-width: 1199px) and (min-width: 900px) {
    width: 320px;
    height: calc(100vh - 290px);
  }

  @media (max-width: 899px) {
    width: 100%;
    height: calc(100vh - 250px);
  }
  
  @media (max-width: 899px) and (min-width: 600px) {
    width: 300px;
    min-width: 300px;
    height: calc(100vh - 275px);
  }
  
  @media (max-width: 599px) {
    width: 100%;
    height: 350px;
    flex-shrink: 1;
  }
  
  ${props => props.$isOver && `
    transform: translateY(-4px) scale(1.02);
    box-shadow: 
      0 30px 70px rgba(0, 0, 0, 0.35),
      0 0 30px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    background: rgba(45, 27, 105, 0.95);
  `}
  
  &:hover {
    transform: ${props => props.$isOver ? 'translateY(-4px) scale(1.02)' : 'translateY(-2px)'};
    box-shadow: 
      0 25px 60px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
`;

const ColumnHeader = styled.div`
  padding: 24px 24px 20px 24px;
  border-bottom: 1px solid rgba(99, 102, 241, 0.3);
  background: rgba(26, 27, 62, 0.6);
  border-radius: 20px 20px 0 0;
  position: sticky;
  top: 0;
  z-index: 10;
  flex-shrink: 0;
  
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ColumnTitle = styled.h3`
  color: #f8fafc;
  font-size: 1.3rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  margin: 0;
  text-shadow: none;
  letter-spacing: -0.01em;
`;

const Counter = styled.span`
  background: rgba(6, 182, 212, 0.9);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(6, 182, 212, 0.4);
`;

const CollapseButton = styled.button<{ $collapsed: boolean }>`
  background: rgba(245, 158, 11, 0.9);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 8px;

  &:hover {
    background: rgba(245, 158, 11, 1);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.5);
  }
`;

const ColumnContent = styled.div<{ $isOver: boolean; $collapsed: boolean }>`
  padding: 20px;
  background: ${props => props.$isOver
    ? 'rgba(6, 182, 212, 0.2)'
    : 'transparent'};
  border-radius: 0 0 16px 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.4) transparent;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(99, 102, 241, 0.4);
    border-radius: 3px;
    
    &:hover {
      background: rgba(99, 102, 241, 0.6);
    }
  }
  
  ${props => props.$collapsed && `
    display: none;
  `}
  
  ${props => props.$isOver && `
    background: rgba(6, 182, 212, 0.25);
    box-shadow: inset 0 0 20px rgba(6, 182, 212, 0.15);
  `}
`;

const CollapsedView = styled.div`
  padding: 20px;
  text-align: center;
  color: #e2e8f0;
  cursor: pointer;
  border-radius: 0 0 16px 16px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background: rgba(6, 182, 212, 0.15);
  }
`;

const CollapsedText = styled.div`
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 8px;
`;

const CollapsedSubtext = styled.div`
  font-size: 0.875rem;
  opacity: 0.8;
`;

const AddNoteButton = styled.button`
  width: 100%;
  padding: 14px 16px;
  margin: 0 0 16px 0;
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.9);
  border: 2px dashed rgba(99, 102, 241, 0.4);
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.9rem;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.6);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.2);
  }

  &:active {
    transform: translateY(0) scale(0.98);
    background: rgba(99, 102, 241, 0.2);
  }
`;

const EmptyState = styled.div<{ $isOver: boolean }>`
  text-align: center;
  color: rgba(99, 102, 241, 0.7);
  font-style: italic;
  margin: 20px 0;
  padding: 40px 20px;
  border: 2px dashed ${props => props.$isOver 
    ? 'rgba(6, 182, 212, 0.8)' 
    : 'rgba(99, 102, 241, 0.4)'};
  border-radius: 12px;
  background: ${props => props.$isOver 
    ? 'rgba(6, 182, 212, 0.12)' 
    : 'transparent'};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 1rem;
  pointer-events: none;
  position: relative;
  min-height: 200px;
  
  ${props => props.$isOver && `
    color: rgba(6, 182, 212, 1);
    transform: scale(1.02);
    border-style: solid;
    box-shadow: 
      0 0 30px rgba(6, 182, 212, 0.3),
      inset 0 0 20px rgba(6, 182, 212, 0.15);
  `}
`;

const EmptyStateIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 12px;
  opacity: 0.7;
`;

const EmptyStateTitle = styled.div`
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
`;

const EmptyStateSubtext = styled.div`
  font-size: 0.85rem;
  opacity: 0.7;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.6);
`;

const statusTitles: Record<Status, string> = {
  todo: '📌 Todo',
  processing: '🔄 Processing',
  done: '✅ Done'
};

const Column: React.FC<ColumnProps> = ({
  status,
  notes,
  onNoteEdit,
  onNoteDelete,
  onCreateNote,
  onQuickCreateNote
}) => {
  // localStorage key for collapsed state
  const localStorageKey = `column-${status}-collapsed`;
  
  // Initialize collapsed state from localStorage
  const [collapsed, setCollapsed] = useState(() => {
    if (status !== 'done') return false;
    const stored = localStorage.getItem(localStorageKey);
    return stored === 'true';
  });
  
  const { isOver, setNodeRef } = useDroppable({
    id: status,
  });

  // Save collapsed state to localStorage whenever it changes
  useEffect(() => {
    if (status === 'done') {
      localStorage.setItem(localStorageKey, collapsed.toString());
    }
  }, [collapsed, localStorageKey, status]);

  const handleQuickCreate = (content: string, noteStatus: Status) => {
    if (onQuickCreateNote) {
      onQuickCreateNote(content, noteStatus);
    }
  };
  
  

  // Memoize sorted notes and note IDs to prevent unnecessary recalculations
  const sortedNotes = useMemo(() => notes, [notes]);
  const noteIds = useMemo(() => sortedNotes.map(note => note.id), [sortedNotes]);

  const handleToggleCollapse = () => {
    setCollapsed(!collapsed);
  };

  const renderContent = () => {
    if (status === 'done' && collapsed) {
      return (
        <CollapsedView onClick={handleToggleCollapse}>
          <CollapsedText>📦 {notes.length} ghi chú đã hoàn thành</CollapsedText>
          <CollapsedSubtext>Nhấp để xem chi tiết</CollapsedSubtext>
        </CollapsedView>
      );
    }

    return (
      <ColumnContent $isOver={isOver} $collapsed={false}>
        {/* Quick add note for Todo and Processing columns */}
        {(status === 'todo' || status === 'processing') && onQuickCreateNote && (
          <QuickAddNote status={status} onCreateNote={handleQuickCreate} />
        )}

        <SortableContext items={noteIds} strategy={verticalListSortingStrategy}>
          {notes.length === 0 ? (
            <EmptyState $isOver={isOver}>
              {isOver ? (
                <>
                  <EmptyStateIcon>🎯</EmptyStateIcon>
                  <EmptyStateTitle>Thả ghi chú vào đây</EmptyStateTitle>
                  <EmptyStateSubtext>Kéo một ghi chú từ cột khác để thay đổi trạng thái</EmptyStateSubtext>
                </>
              ) : (
                <>
                  <EmptyStateIcon>
                    {status === 'todo' && '📝'}
                    {status === 'processing' && '⚡'}
                    {status === 'done' && '✅'}
                  </EmptyStateIcon>
                  <EmptyStateTitle>
                    {status === 'todo' && 'Chưa có công việc nào'}
                    {status === 'processing' && 'Chưa có công việc đang xử lý'}
                    {status === 'done' && 'Chưa hoàn thành công việc nào'}
                  </EmptyStateTitle>
                  <EmptyStateSubtext>
                    {status === 'todo' && 'Nhấn nút "Tạo ghi chú mới" để bắt đầu'}
                    {status === 'processing' && 'Kéo công việc từ Todo để bắt đầu xử lý'}
                    {status === 'done' && 'Hoàn thành công việc để thấy chúng ở đây'}
                  </EmptyStateSubtext>
                </>
              )}
            </EmptyState>
          ) : (
            sortedNotes.map((note) => (
              <StickyNote
                key={note.id}
                note={note}
                onEdit={onNoteEdit}
                onDelete={onNoteDelete}
              />
            ))
          )}
        </SortableContext>
      </ColumnContent>
    );
  };

  return (
    <ColumnContainer ref={setNodeRef} $isOver={isOver}>
      <ColumnHeader>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ColumnTitle>{statusTitles[status]}</ColumnTitle>
          <Counter>({notes.length})</Counter>
          {isOver && <span style={{ marginLeft: '8px', fontSize: '1.2rem' }}>🎯</span>}
        </div>
        {status === 'done' && notes.length > 0 && (
          <CollapseButton $collapsed={collapsed} onClick={handleToggleCollapse}>
            {collapsed ? 'Mở rộng' : 'Thu gọn'}
          </CollapseButton>
        )}
      </ColumnHeader>
      {renderContent()}
    </ColumnContainer>
  );
};

// Memoize Column component to prevent unnecessary re-renders
export default memo(Column, (prevProps, nextProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.status === nextProps.status &&
    prevProps.notes.length === nextProps.notes.length &&
    prevProps.notes.every((note, index) =>
      note.id === nextProps.notes[index]?.id &&
      note.updatedAt === nextProps.notes[index]?.updatedAt
    )
  );
});