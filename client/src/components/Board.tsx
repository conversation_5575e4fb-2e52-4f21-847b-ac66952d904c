import React, { useState, useEffect } from 'react';
import { DndContext, DragOverlay, closestCenter, pointerWithin, rectIntersection } from '@dnd-kit/core';
import type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import toast from 'react-hot-toast';
import Column from './Column';
import StickyNote from './StickyNote';
import NoteEditorModal from './NoteEditorModal';
import { notesApi } from '../services/api';
import type { StickyNote as StickyNoteType, CreateNotePayload, Status } from '../types';
import { <PERSON><PERSON><PERSON><PERSON>, Header, BoardContent } from '../styles/GlobalStyles';
import { addNoteWithPrioritySort, updateNoteWithPrioritySort, removeNoteFromList, getColorByPriority } from '../utils/noteUtils';

const Board: React.FC = () => {
  const [notes, setNotes] = useState<StickyNoteType[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingNote, setEditingNote] = useState<StickyNoteType | null>(null);
  const [activeNote, setActiveNote] = useState<StickyNoteType | null>(null);
  // Removed selection mode to avoid conflict with drag & drop

  // Fetch notes on component mount
  useEffect(() => {
    fetchNotes();
  }, []);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+N (Windows/Linux) or Cmd+N (Mac) to create new note in Todo
      if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        // Prevent browser's default "New Window" action
        event.preventDefault();

        // Don't trigger if user is typing in an input/textarea
        const activeElement = document.activeElement;
        if (activeElement && (
          activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          (activeElement as HTMLElement).contentEditable === 'true'
        )) {
          return;
        }

        // Create new note in Todo column
        handleCreateNote('todo');
        toast.success('⚡ Tạo ghi chú mới (Ctrl/Cmd+N)');
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []); // Empty dependency array since we don't need to recreate this

  const fetchNotes = async () => {
    try {
      setLoading(true);
      const fetchedNotes = await notesApi.getAllNotes();
      // Removed console.log for better performance
      setNotes(fetchedNotes);
      // Removed success toast to avoid unnecessary notifications
    } catch (error) {
      console.error('Error fetching notes:', error);
      toast.error('Không thể tải dữ liệu. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNote = (status?: Status) => {
    // If status is provided, store it for the modal to use
    if (status) {
      setEditingNote({ status } as any); // This will be used as initial data, not as existing note
    } else {
      setEditingNote(null);
    }
    setIsModalOpen(true);
  };

  const handleEditNote = (note: StickyNoteType) => {
    setEditingNote(note);
    setIsModalOpen(true);
  };

  const handleQuickCreateNote = async (content: string, status: Status) => {
    try {
      const newNote = await notesApi.createNote({
        content,
        status,
        color: getColorByPriority('medium'), // Default priority for quick notes
        priority: 'medium'
      });
      // Add to state and sort by priority using utility function
      setNotes(prevNotes => addNoteWithPrioritySort(prevNotes, newNote));
      toast.success(`⚡ Đã tạo nhanh ghi chú trong cột ${status.toUpperCase()}`);
    } catch (error) {
      console.error('Error creating quick note:', error);
      toast.error('Không thể tạo ghi chú. Vui lòng thử lại.');
    }
  };

  const handleSaveNote = async (data: CreateNotePayload) => {
    try {
      if (editingNote && editingNote.id) {
        // Update existing note
        const updatedNote = await notesApi.updateNote(editingNote.id, data);
        setNotes(prevNotes => updateNoteWithPrioritySort(prevNotes, updatedNote.id, updatedNote));
        toast.success(`✅ Đã cập nhật "${data.title || 'ghi chú'}" thành công`);
      } else {
        // Create new note - use status from editingNote if available
        const createData = {
          ...data,
          status: data.status || (editingNote?.status as Status) || 'todo'
        };
        const newNote = await notesApi.createNote(createData);
        setNotes(prevNotes => addNoteWithPrioritySort(prevNotes, newNote));
        toast.success(`🎉 Đã tạo "${data.title || 'ghi chú mới'}" trong cột ${createData.status?.toUpperCase()}`);
      }
    } catch (error) {
      console.error('Error saving note:', error);
      toast.error('Không thể lưu ghi chú. Vui lòng thử lại.');
    }
  };

  const handleDeleteNote = async (id: string) => {
    try {
      await notesApi.deleteNote(id);
      // Remove from state using utility function
      setNotes(prevNotes => removeNoteFromList(prevNotes, id));
      toast.success('Đã xóa ghi chú thành công');
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error('Không thể xóa ghi chú. Vui lòng thử lại.');
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const draggedNote = notes.find(note => note.id === active.id);
    setActiveNote(draggedNote || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveNote(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    const activeNote = notes.find(note => note.id === activeId);
    if (!activeNote) return;

    // Determine if we're dropping on a column or another note
    const isDroppedOnColumn = ['todo', 'processing', 'done'].includes(overId);
    const targetStatus: Status = isDroppedOnColumn ? overId as Status :
      notes.find(note => note.id === overId)?.status || activeNote.status;

    // If no status change and dropped on column, do nothing
    if (targetStatus === activeNote.status && isDroppedOnColumn) {
      return;
    }

    try {
      // Simplified: Just update the dragged note's status
      if (targetStatus !== activeNote.status) {
        // Update UI immediately for better UX
        setNotes(prevNotes =>
          prevNotes.map(note =>
            note.id === activeId
              ? { ...note, status: targetStatus }
              : note
          )
        );

        // Send simple update to server
        const updatedNote = await notesApi.updateNote(activeId, { status: targetStatus });

        // Update state with server response to ensure consistency
        setNotes(prevNotes =>
          prevNotes.map(note =>
            note.id === activeId ? updatedNote : note
          )
        );

        toast.success(`🔄 Đã di chuyển ghi chú sang cột ${targetStatus.toUpperCase()}`);
      }
    } catch (error) {
      console.error('Error moving note:', error);
      toast.error('Không thể di chuyển ghi chú. Vui lòng thử lại.');
      // Revert optimistic update by restoring original status
      setNotes(prevNotes =>
        prevNotes.map(note =>
          note.id === activeId
            ? { ...note, status: activeNote.status }
            : note
        )
      );
    }
  };

  // Removed selection mode functions to avoid conflict with drag & drop


  // Group notes by status - API already returns sorted data by priority
  const notesByStatus = {
    todo: notes.filter(note => note.status === 'todo'),
    processing: notes.filter(note => note.status === 'processing'),
    done: notes.filter(note => note.status === 'done')
  };

  // Removed console.log for better performance

  // Custom collision detection that works better with empty droppables
  const customCollisionDetection = (args: any) => {
    // First try pointerWithin for empty droppables
    const pointerCollisions = pointerWithin(args);
    if (pointerCollisions.length > 0) {
      return pointerCollisions;
    }
    
    // Fallback to rectIntersection
    const intersectionCollisions = rectIntersection(args);
    if (intersectionCollisions.length > 0) {
      return intersectionCollisions;
    }
    
    // Final fallback to closestCenter
    return closestCenter(args);
  };

  if (loading) {
    return (
      <BoardContainer>
        <Header>
          <h1>📌 Quản lý công việc</h1>
        </Header>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          height: '50vh',
          color: '#F4E4BC',
          fontSize: '1.2rem'
        }}>
          Đang tải dữ liệu...
        </div>
      </BoardContainer>
    );
  }

  return (
    <BoardContainer>
      <Header style={{ textAlign: 'center' }}>
        <h1 style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '12px',
          margin: 0,
          color: '#f8fafc'
        }}>
          <span style={{
            width: '32px',
            height: '32px',
            background: 'linear-gradient(135deg, #c7d2fe 0%, #a5b4fc 100%)',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            boxShadow: '0 4px 12px rgba(199, 210, 254, 0.3)'
          }}>
            📌
          </span>
          Quản lý công việc
        </h1>
        <p>Ghim và sắp xếp các ghi chú theo trạng thái công việc</p>
        <div style={{
          fontSize: '0.85rem',
          color: '#94a3b8',
          marginTop: '8px',
          display: 'flex',
          alignItems: 'center',
          gap: '16px'
        }}>
        </div>
      </Header>
      

      {/* Removed selection mode UI to avoid conflict with drag & drop */}

      <DndContext
        collisionDetection={customCollisionDetection}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <BoardContent>
          <Column
            status="todo"
            notes={notesByStatus.todo}
            onNoteEdit={handleEditNote}
            onNoteDelete={handleDeleteNote}
            onCreateNote={handleCreateNote}
            onQuickCreateNote={handleQuickCreateNote}
          />
          <Column
            status="processing"
            notes={notesByStatus.processing}
            onNoteEdit={handleEditNote}
            onNoteDelete={handleDeleteNote}
            onCreateNote={handleCreateNote}
            onQuickCreateNote={handleQuickCreateNote}
          />
          <Column
            status="done"
            notes={notesByStatus.done}
            onNoteEdit={handleEditNote}
            onNoteDelete={handleDeleteNote}
          />
        </BoardContent>

        <DragOverlay>
          {activeNote && (
            <StickyNote
              note={activeNote}
              onEdit={() => {}}
              onDelete={() => {}}
            />
          )}
        </DragOverlay>
      </DndContext>

      <NoteEditorModal
        isOpen={isModalOpen}
        note={editingNote}
        onSave={handleSaveNote}
        onClose={() => {
          setIsModalOpen(false);
          setEditingNote(null);
        }}
      />
    </BoardContainer>
  );
};

export default Board;