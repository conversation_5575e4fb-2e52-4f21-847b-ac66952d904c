import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import TabNavigation from './TabNavigation';
import type { TabType } from '../types';
import EisenhowerMatrix from './EisenhowerMatrix';
import TaskListView from './TaskListView';
import CompletedProjectsView from './CompletedProjectsView';
import NoteEditorModal from './NoteEditorModal';
import { notesApi } from '../services/api';
import type { StickyNote as StickyNoteType, CreateNotePayload, Quadrant } from '../types';
import { BoardContainer, Header } from '../styles/GlobalStyles';
import { addNoteWithPrioritySort, updateNoteWithPrioritySort, removeNoteFromList, getColorByPriority } from '../utils/noteUtils';

const MainApp: React.FC = () => {
  const [notes, setNotes] = useState<StickyNoteType[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<TabType>('matrix');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingNote, setEditingNote] = useState<StickyNoteType | null>(null);

  // Load notes on component mount
  useEffect(() => {
    loadNotes();
  }, []);

  const loadNotes = async () => {
    try {
      setLoading(true);
      const fetchedNotes = await notesApi.getAllNotes();
      setNotes(fetchedNotes);
    } catch (error) {
      console.error('Error loading notes:', error);
      toast.error('Không thể tải danh sách ghi chú');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNote = (quadrant?: Quadrant) => {
    const newNote: Partial<StickyNoteType> = {
      title: '',
      description: '',
      quadrant: quadrant || 'important_not_urgent',
      color: getColorByPriority('medium'),
      priority: 'medium',
      tags: [],
      assigner: '',
      deadline: ''
    };
    setEditingNote(newNote as StickyNoteType);
    setIsModalOpen(true);
  };

  const handleQuickCreateNote = async (description: string, quadrant: Quadrant) => {
    try {
      const noteData: CreateNotePayload = {
        title: description.length > 50 ? description.substring(0, 50) + '...' : description,
        description,
        quadrant,
        color: getColorByPriority('medium'),
        priority: 'medium',
        tags: []
      };

      const newNote = await notesApi.createNote(noteData);
      setNotes(prevNotes => addNoteWithPrioritySort(prevNotes, newNote));
      toast.success('Ghi chú đã được tạo thành công');
    } catch (error) {
      console.error('Error creating note:', error);
      toast.error('Không thể tạo ghi chú');
    }
  };

  const handleEditNote = (note: StickyNoteType) => {
    setEditingNote(note);
    setIsModalOpen(true);
  };

  // Separate function for drag-and-drop updates that directly updates the database
  const handleDragDropUpdate = async (updatedNote: StickyNoteType) => {
    try {
      const savedNote = await notesApi.updateNote(updatedNote.id, {
        title: updatedNote.title,
        description: updatedNote.description,
        quadrant: updatedNote.quadrant,
        color: updatedNote.color,
        priority: updatedNote.priority,
        assigner: updatedNote.assigner,
        deadline: updatedNote.deadline,
        tags: updatedNote.tags
      });

      setNotes(prevNotes =>
        updateNoteWithPrioritySort(prevNotes, updatedNote.id, savedNote)
      );
    } catch (error) {
      console.error('Error updating note:', error);
      toast.error('Không thể cập nhật ghi chú');
      throw error; // Re-throw to let drag-and-drop handle the error
    }
  };

  const handleSaveNote = async (noteData: Partial<StickyNoteType>) => {
    try {
      if (editingNote?.id) {
        // Update existing note
        const updatedNote = await notesApi.updateNote(editingNote.id, {
          title: noteData.title,
          description: noteData.description,
          color: noteData.color,
          quadrant: noteData.quadrant,
          priority: noteData.priority,
          assigner: noteData.assigner,
          deadline: noteData.deadline,
          tags: noteData.tags
        });

        setNotes(prevNotes => updateNoteWithPrioritySort(prevNotes, updatedNote.id, updatedNote));
        toast.success('Ghi chú đã được cập nhật');
      } else {
        // Create new note
        const newNote = await notesApi.createNote({
          title: noteData.title || '',
          description: noteData.description || '',
          color: noteData.color,
          quadrant: noteData.quadrant,
          priority: noteData.priority,
          assigner: noteData.assigner,
          deadline: noteData.deadline,
          tags: noteData.tags
        });

        setNotes(prevNotes => addNoteWithPrioritySort(prevNotes, newNote));
        toast.success('Ghi chú đã được tạo thành công');
      }
    } catch (error) {
      console.error('Error saving note:', error);
      toast.error('Không thể lưu ghi chú');
    }
  };

  const handleDeleteNote = async (id: string) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa ghi chú này?')) {
      return;
    }

    try {
      await notesApi.deleteNote(id);
      setNotes(prevNotes => removeNoteFromList(prevNotes, id));
      toast.success('Ghi chú đã được xóa');
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error('Không thể xóa ghi chú');
    }
  };

  const handleNoteUpdate = async (updatedNote: StickyNoteType) => {
    try {
      const savedNote = await notesApi.updateNote(updatedNote.id, {
        title: updatedNote.title,
        description: updatedNote.description,
        quadrant: updatedNote.quadrant,
        order: updatedNote.order,
        color: updatedNote.color,
        priority: updatedNote.priority,
        assigner: updatedNote.assigner,
        deadline: updatedNote.deadline,
        tags: updatedNote.tags,
        completed: updatedNote.completed
      });

      setNotes(prevNotes => updateNoteWithPrioritySort(prevNotes, savedNote.id, savedNote));
    } catch (error) {
      console.error('Error updating note:', error);
      toast.error('Không thể cập nhật ghi chú');
      // Reload notes to revert changes
      loadNotes();
    }
  };

  const handleToggleComplete = async (note: StickyNoteType) => {
    try {
      const updatedNote = {
        ...note,
        completed: !note.completed
      };

      const savedNote = await notesApi.updateNote(note.id, {
        completed: updatedNote.completed
      });

      setNotes(prevNotes => updateNoteWithPrioritySort(prevNotes, note.id, savedNote));

      // Show success message without switching tabs
      if (updatedNote.completed) {
        toast.success('Task completed!');
      } else {
        toast.success('Task marked as incomplete');
      }
    } catch (error) {
      console.error('Error toggling completion:', error);
      toast.error('Unable to update completion status');
    }
  };

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'matrix':
        return (
          <EisenhowerMatrix
            notes={notes}
            onNoteEdit={handleDragDropUpdate}
            onNoteClick={handleEditNote}
            onNoteDelete={handleDeleteNote}
            onCreateNote={handleCreateNote}
            onQuickCreateNote={handleQuickCreateNote}
            onToggleComplete={handleToggleComplete}
          />
        );
      case 'list':
        return (
          <TaskListView
            notes={notes}
            onNoteEdit={handleEditNote}
            onNoteDelete={handleDeleteNote}
            onToggleComplete={handleToggleComplete}
          />
        );
      case 'completed':
        return (
          <CompletedProjectsView
            notes={notes}
            onNoteEdit={handleNoteUpdate}
            onNoteDelete={handleDeleteNote}
            onToggleComplete={handleToggleComplete}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <BoardContainer>
        <Header>
          <h1>🗂️ Task Manager</h1>
        </Header>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          fontSize: '18px',
          color: '#666'
        }}>
          Loading...
        </div>
      </BoardContainer>
    );
  }

  return (
    <BoardContainer>
      <Header>
        <h1>🗂️ Task Manager</h1>
        <p style={{ margin: '8px 0 0 0', color: '#666', fontSize: '14px' }}>
          Organize tasks by importance and urgency
        </p>
      </Header>

      <TabNavigation 
        activeTab={activeTab} 
        onTabChange={setActiveTab} 
      />

      {renderActiveTab()}

      <NoteEditorModal
        isOpen={isModalOpen}
        note={editingNote}
        onSave={handleSaveNote}
        onClose={() => {
          setIsModalOpen(false);
          setEditingNote(null);
        }}
      />
    </BoardContainer>
  );
};

export default MainApp;
