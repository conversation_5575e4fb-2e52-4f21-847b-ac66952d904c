import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import type { StickyNote as StickyNoteType, Quadrant } from '../types';

const Container = styled.div`
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  padding: 24px;
  height: calc(100vh - 200px);
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
  flex-shrink: 0; /* Prevent header from shrinking */
`;

const Title = styled.h2`
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #667eea;
  }
`;

const SearchInput = styled.input`
  padding: 8px 12px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  min-width: 200px;
  
  &:focus {
    outline: none;
    border-color: #667eea;
  }
`;

const TaskList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  padding-right: 4px; /* Space for scrollbar */

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }
`;

const TaskItem = styled.div<{ $quadrant: Quadrant }>`
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid ${props => {
    switch (props.$quadrant) {
      case 'important_urgent': return '#F44336';
      case 'important_not_urgent': return '#4CAF50';
      case 'not_important_urgent': return '#FF9800';
      case 'not_important_not_urgent': return '#9E9E9E';
      default: return '#dee2e6';
    }
  }};
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  }
`;

const TaskHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const TaskTitle = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
`;

const TaskMeta = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
`;

const QuadrantBadge = styled.span<{ $quadrant: Quadrant }>`
  background: ${props => {
    switch (props.$quadrant) {
      case 'important_urgent': return '#F44336';
      case 'important_not_urgent': return '#4CAF50';
      case 'not_important_urgent': return '#FF9800';
      case 'not_important_not_urgent': return '#9E9E9E';
      default: return '#dee2e6';
    }
  }};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
`;

const PriorityBadge = styled.span<{ $priority: string }>`
  background: ${props => {
    switch (props.$priority) {
      case 'urgent': return '#dc3545';
      case 'high': return '#fd7e14';
      case 'medium': return '#ffc107';
      case 'low': return '#28a745';
      default: return '#6c757d';
    }
  }};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
`;

const TaskDescription = styled.p`
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
`;

const TaskFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  font-size: 12px;
  color: #999;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
`;

interface TaskListViewProps {
  notes: StickyNoteType[];
  onNoteEdit: (note: StickyNoteType) => void;
  onNoteDelete: (id: string) => void;
  onToggleComplete?: (note: StickyNoteType) => void;
}

const TaskListView: React.FC<TaskListViewProps> = ({ notes, onNoteEdit, onNoteDelete, onToggleComplete }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterQuadrant, setFilterQuadrant] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created');

  // Temporary: Show all incomplete notes for debugging
  const [showAllIncomplete, setShowAllIncomplete] = useState(false);

  const filteredAndSortedNotes = useMemo(() => {
    // Debug: Count notes by completion status
    const completedCount = notes.filter(n => n.completed === true).length;
    const incompleteCount = notes.filter(n => n.completed !== true).length;
    console.log(`TaskListView: Total=${notes.length}, Completed=${completedCount}, Incomplete=${incompleteCount}`);
    console.log(`TaskListView: Filters - Search="${searchTerm}", Quadrant="${filterQuadrant}", Priority="${filterPriority}"`);

    let filtered = notes.filter(note => {
      // Only show incomplete notes (handle undefined/null/false as incomplete)
      const isIncomplete = note.completed !== true;

      const matchesSearch = !searchTerm ||
        note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesQuadrant = filterQuadrant === 'all' || note.quadrant === filterQuadrant;
      const matchesPriority = filterPriority === 'all' || note.priority === filterPriority;

      const passes = showAllIncomplete ? isIncomplete : (isIncomplete && matchesSearch && matchesQuadrant && matchesPriority);

      // Debug each note
      if (!passes) {
        console.log(`❌ Note ${note.id} filtered out:`, {
          title: note.title?.substring(0, 30) || note.description?.substring(0, 30),
          isIncomplete,
          matchesSearch,
          matchesQuadrant,
          matchesPriority,
          completed: note.completed
        });
      }

      return passes;
    });

    console.log(`TaskListView: After filtering=${filtered.length}`);

    // Sort notes
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
          return priorityOrder[a.priority || 'medium'] - priorityOrder[b.priority || 'medium'];
        case 'deadline':
          if (!a.deadline && !b.deadline) return 0;
          if (!a.deadline) return 1;
          if (!b.deadline) return -1;
          return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'created':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    return filtered;
  }, [notes, searchTerm, filterQuadrant, filterPriority, sortBy]);

  const getQuadrantLabel = (quadrant: Quadrant): string => {
    switch (quadrant) {
      case 'important_urgent': return 'Gấp rút';
      case 'important_not_urgent': return 'Lên lịch';
      case 'not_important_urgent': return 'Ủy thác';
      case 'not_important_not_urgent': return 'Loại bỏ';
      default: return '';
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Container>
      <Header>
        <Title>
          📋 Danh sách Task
          <span style={{ fontSize: '16px', fontWeight: 'normal', color: '#666' }}>
            ({filteredAndSortedNotes.length} task)
          </span>
          <button
            onClick={() => setShowAllIncomplete(!showAllIncomplete)}
            style={{
              marginLeft: '10px',
              padding: '4px 8px',
              fontSize: '12px',
              background: showAllIncomplete ? '#22c55e' : '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {showAllIncomplete ? 'Show Filtered' : 'Show All Incomplete'}
          </button>
        </Title>
        
        <FilterContainer>
          <SearchInput
            type="text"
            placeholder="Tìm kiếm task..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          
          <FilterSelect
            value={filterQuadrant}
            onChange={(e) => setFilterQuadrant(e.target.value)}
          >
            <option value="all">Tất cả phân loại</option>
            <option value="important_urgent">Việc gấp rút</option>
            <option value="important_not_urgent">Lên lịch thực hiện</option>
            <option value="not_important_urgent">Việc có thể ủy thác</option>
            <option value="not_important_not_urgent">Loại bỏ</option>
          </FilterSelect>
          
          <FilterSelect
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
          >
            <option value="all">Tất cả mức độ</option>
            <option value="urgent">Khẩn cấp</option>
            <option value="high">Cao</option>
            <option value="medium">Trung bình</option>
            <option value="low">Thấp</option>
          </FilterSelect>
          
          <FilterSelect
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="created">Ngày tạo</option>
            <option value="priority">Mức độ ưu tiên</option>
            <option value="deadline">Hạn chót</option>
            <option value="title">Tên task</option>
          </FilterSelect>
        </FilterContainer>
      </Header>

      <TaskList>
        {filteredAndSortedNotes.length === 0 ? (
          <EmptyState>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
            <h3>Không tìm thấy task nào</h3>
            <p>Thử thay đổi bộ lọc hoặc tạo task mới</p>
          </EmptyState>
        ) : (
          filteredAndSortedNotes.map(note => (
            <TaskItem key={note.id} $quadrant={note.quadrant}>
              <TaskHeader>
                <TaskTitle>{note.title || 'Không có tiêu đề'}</TaskTitle>
                <TaskMeta>
                  <QuadrantBadge $quadrant={note.quadrant}>
                    {getQuadrantLabel(note.quadrant)}
                  </QuadrantBadge>
                  <PriorityBadge $priority={note.priority || 'medium'}>
                    {note.priority || 'medium'}
                  </PriorityBadge>
                </TaskMeta>
              </TaskHeader>
              
              {note.description && (
                <TaskDescription>{note.description}</TaskDescription>
              )}
              
              <TaskFooter>
                <div>
                  Tạo: {formatDate(note.createdAt)}
                  {note.deadline && (
                    <span style={{ marginLeft: '16px' }}>
                      Hạn: {formatDate(note.deadline)}
                    </span>
                  )}
                  {note.assigner && (
                    <span style={{ marginLeft: '16px' }}>
                      Người thực hiện: {note.assigner}
                    </span>
                  )}
                </div>
                
                <ActionButtons>
                  {onToggleComplete && (
                    <ActionButton
                      onClick={() => onToggleComplete(note)}
                      style={{
                        background: 'rgba(34, 197, 94, 0.1)',
                        color: '#16a34a',
                        border: '1px solid rgba(34, 197, 94, 0.3)'
                      }}
                    >
                      ✅ Complete
                    </ActionButton>
                  )}
                  <ActionButton onClick={() => onNoteEdit(note)}>
                    ✏️ Sửa
                  </ActionButton>
                  <ActionButton onClick={() => onNoteDelete(note.id)}>
                    🗑️ Xóa
                  </ActionButton>
                </ActionButtons>
              </TaskFooter>
            </TaskItem>
          ))
        )}
      </TaskList>
    </Container>
  );
};

export default TaskListView;
