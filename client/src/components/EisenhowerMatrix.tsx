import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import { DndContext, DragOverlay, closestCenter, pointerWithin, rectIntersection } from '@dnd-kit/core';
import type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import toast from 'react-hot-toast';
import StickyNote from './StickyNote';
import Quadrant from './Quadrant';
import type { StickyNote as StickyNoteType, Quadrant as QuadrantType, EisenhowerMatrixProps } from '../types';

const MatrixContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr; /* Equal columns for all quadrants */
  grid-template-rows: 1fr 1fr; /* Equal rows for all quadrants */
  gap: 12px; /* Reduced gap for better spacing */
  height: calc(100vh - 160px); /* Increased reduction to create more bottom space */
  width: 100%; /* Ensure full width */
  max-width: 100%; /* Prevent overflow */
  min-height: 500px; /* Reduced min-height */
  max-height: calc(100vh - 160px);
  padding: 8px; /* Simplified padding */
  margin-bottom: 15px; /* Increased bottom margin to ensure spacing */
  box-sizing: border-box; /* Include padding in width calculation */
  overflow: hidden; /* Prevent any overflow */

  /* Responsive design */
  @media (max-width: 1024px) {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    height: calc(100vh - 140px); /* Increased reduction for bottom spacing */
    width: 100%;
    max-width: 100%;
    min-height: 450px;
    max-height: calc(100vh - 140px);
    padding: 6px; /* Simplified padding */
    margin-bottom: 12px; /* Increased bottom margin */
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr; /* Single column on mobile */
    grid-template-rows: repeat(4, 1fr); /* Stack all quadrants vertically with equal heights */
    gap: 8px;
    height: calc(100vh - 120px); /* Increased reduction for bottom spacing */
    width: 100%;
    max-width: 100%;
    min-height: 600px; /* Reduced for mobile */
    max-height: none; /* Allow scrolling on mobile */
    padding: 4px; /* Simplified padding */
    margin-bottom: 10px; /* Increased bottom margin */
  }
`;

/* Axis labels removed - no longer needed */

const QuadrantContainer = styled.div<{ $quadrant: QuadrantType }>`
  /* Ensure all quadrants have uniform dimensions */
  width: 100%;
  height: 100%;
  min-height: 280px; /* Consistent minimum height for all quadrants */
  max-height: 100%; /* Prevent overflow */
  overflow: hidden; /* Ensure content doesn't overflow */

  ${props => {
    switch (props.$quadrant) {
      case 'important_urgent':
        return `
          grid-column: 2;
          grid-row: 1;
        `;
      case 'important_not_urgent':
        return `
          grid-column: 1;
          grid-row: 1;
        `;
      case 'not_important_urgent':
        return `
          grid-column: 2;
          grid-row: 2;
        `;
      case 'not_important_not_urgent':
        return `
          grid-column: 1;
          grid-row: 2;
        `;
      default:
        return '';
    }
  }}

  /* Responsive design for mobile */
  @media (max-width: 1024px) {
    min-height: 250px; /* Slightly smaller on tablets */
  }

  @media (max-width: 768px) {
    min-height: 200px; /* Smaller on mobile but still uniform */
    max-height: none; /* Allow natural height on mobile */

    ${props => {
      switch (props.$quadrant) {
        case 'important_urgent':
          return `
            grid-column: 1;
            grid-row: 1;
          `;
        case 'important_not_urgent':
          return `
            grid-column: 1;
            grid-row: 2;
          `;
        case 'not_important_urgent':
          return `
            grid-column: 1;
            grid-row: 3;
          `;
        case 'not_important_not_urgent':
          return `
            grid-column: 1;
            grid-row: 4;
          `;
        default:
          return '';
      }
    }}
  }
`;

const EisenhowerMatrix: React.FC<EisenhowerMatrixProps> = ({
  notes,
  onNoteEdit,
  onNoteClick,
  onNoteDelete,
  onCreateNote,
  onQuickCreateNote,
  onToggleComplete
}) => {
  const [activeNote, setActiveNote] = useState<StickyNoteType | null>(null);

  // Group notes by quadrant (only show incomplete notes)
  const notesByQuadrant = useMemo(() => {
    const grouped = {
      important_urgent: [] as StickyNoteType[],
      important_not_urgent: [] as StickyNoteType[],
      not_important_urgent: [] as StickyNoteType[],
      not_important_not_urgent: [] as StickyNoteType[]
    };

    // Only include notes that are not completed (handle undefined/null as incomplete)
    const incompleteNotes = notes.filter(note => note.completed !== true);

    incompleteNotes.forEach(note => {
      if (grouped[note.quadrant]) {
        grouped[note.quadrant].push(note);
      }
    });

    return grouped;
  }, [notes]);

  const handleDragStart = (event: DragStartEvent) => {
    const activeId = event.active.id as string;
    const note = notes.find(n => n.id === activeId);
    setActiveNote(note || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveNote(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    const activeNote = notes.find(note => note.id === activeId);
    if (!activeNote) return;

    // Determine if we're dropping on a quadrant, sub-block, or another note
    const isDroppedOnQuadrant = ['important_urgent', 'important_not_urgent', 'not_important_urgent', 'not_important_not_urgent'].includes(overId);
    const isDroppedOnSubBlock = overId.includes('-block-');

    let targetQuadrant: QuadrantType;

    if (isDroppedOnQuadrant) {
      targetQuadrant = overId as QuadrantType;
    } else if (isDroppedOnSubBlock) {
      // Extract quadrant from sub-block ID (e.g., "important_urgent-block-1" -> "important_urgent")
      targetQuadrant = overId.split('-block-')[0] as QuadrantType;
    } else {
      // Dropped on another note
      targetQuadrant = notes.find(note => note.id === overId)?.quadrant || activeNote.quadrant;
    }

    // If no quadrant change and dropped on same quadrant/sub-block, do nothing
    if (targetQuadrant === activeNote.quadrant && (isDroppedOnQuadrant || isDroppedOnSubBlock)) {
      return;
    }

    try {
      // Update the note's quadrant
      await onNoteEdit({
        ...activeNote,
        quadrant: targetQuadrant
      });

      toast.success(`Note moved to ${getQuadrantLabel(targetQuadrant)}`);
    } catch (error) {
      console.error('Error moving note:', error);
      toast.error('Unable to move note');
    }
  };

  const getQuadrantLabel = (quadrant: QuadrantType): string => {
    switch (quadrant) {
      case 'important_urgent':
        return 'Urgent';
      case 'important_not_urgent':
        return 'Schedule';
      case 'not_important_urgent':
        return 'Delegate';
      case 'not_important_not_urgent':
        return 'Eliminate';
      default:
        return '';
    }
  };

  const customCollisionDetection = (args: any) => {
    // First, let's see if there are any collisions with the pointer
    const pointerIntersections = pointerWithin(args);
    if (pointerIntersections.length > 0) {
      return pointerIntersections;
    }

    // If there are no pointer intersections, return rectangle intersections
    return rectIntersection(args);
  };

  return (
    <DndContext
      collisionDetection={customCollisionDetection}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <MatrixContainer>
        <QuadrantContainer $quadrant="important_not_urgent">
          <Quadrant
            quadrant="important_not_urgent"
            title="Schedule"
            subtitle="Important & Not Urgent"
            color="#4CAF50"
            notes={notesByQuadrant.important_not_urgent}
            onNoteEdit={onNoteEdit}
            onNoteClick={onNoteClick}
            onNoteDelete={onNoteDelete}
            onCreateNote={onCreateNote}
            onQuickCreateNote={onQuickCreateNote}
            onToggleComplete={onToggleComplete}
          />
        </QuadrantContainer>

        <QuadrantContainer $quadrant="important_urgent">
          <Quadrant
            quadrant="important_urgent"
            title="Urgent"
            subtitle="Important & Urgent"
            color="#F44336"
            notes={notesByQuadrant.important_urgent}
            onNoteEdit={onNoteEdit}
            onNoteClick={onNoteClick}
            onNoteDelete={onNoteDelete}
            onCreateNote={onCreateNote}
            onQuickCreateNote={onQuickCreateNote}
            onToggleComplete={onToggleComplete}
          />
        </QuadrantContainer>

        <QuadrantContainer $quadrant="not_important_not_urgent">
          <Quadrant
            quadrant="not_important_not_urgent"
            title="Eliminate"
            subtitle="Not Important & Not Urgent"
            color="#9E9E9E"
            notes={notesByQuadrant.not_important_not_urgent}
            onNoteEdit={onNoteEdit}
            onNoteClick={onNoteClick}
            onNoteDelete={onNoteDelete}
            onCreateNote={onCreateNote}
            onQuickCreateNote={onQuickCreateNote}
            onToggleComplete={onToggleComplete}
          />
        </QuadrantContainer>

        <QuadrantContainer $quadrant="not_important_urgent">
          <Quadrant
            quadrant="not_important_urgent"
            title="Delegate"
            subtitle="Not Important & Urgent"
            color="#FF9800"
            notes={notesByQuadrant.not_important_urgent}
            onNoteEdit={onNoteEdit}
            onNoteClick={onNoteClick}
            onNoteDelete={onNoteDelete}
            onCreateNote={onCreateNote}
            onQuickCreateNote={onQuickCreateNote}
            onToggleComplete={onToggleComplete}
          />
        </QuadrantContainer>
      </MatrixContainer>

      <DragOverlay>
        {activeNote && (
          <StickyNote
            note={activeNote}
            onEdit={() => {}}
            onDelete={() => {}}
          />
        )}
      </DragOverlay>
    </DndContext>
  );
};

export default EisenhowerMatrix;
