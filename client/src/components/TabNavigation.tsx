import React from 'react';
import styled from 'styled-components';
import type { TabType } from '../types';

const TabContainer = styled.div`
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px 8px 0 0;
  padding: 4px; /* Reduced padding */
  margin-bottom: 12px; /* Reduced margin */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Tab = styled.button<{ $active: boolean }>`
  flex: 1;
  padding: 8px 12px; /* Reduced padding for compact design */
  border: none;
  border-radius: 6px;
  background: ${props => props.$active
    ? 'rgba(255, 255, 255, 0.95)'
    : 'rgba(255, 255, 255, 0.1)'};
  color: ${props => props.$active ? '#333' : 'rgba(255, 255, 255, 0.9)'};
  font-weight: ${props => props.$active ? '600' : '500'};
  font-size: 13px; /* Smaller font for compact design */
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 2px;
  min-height: 36px; /* Reduced minimum height */
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: ${props => props.$active
      ? 'rgba(255, 255, 255, 0.95)'
      : 'rgba(255, 255, 255, 0.2)'};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }

  /* Responsive tab heights */
  @media (max-width: 1024px) {
    padding: 6px 10px; /* Reduced padding for tablets */
    font-size: 12px;
    min-height: 32px;
  }

  @media (max-width: 768px) {
    padding: 6px 8px; /* Smaller padding for mobile */
    font-size: 11px;
    min-height: 30px;
    margin: 0 1px; /* Reduced margin on mobile */
  }

  @media (max-width: 480px) {
    padding: 4px 6px; /* Compact padding for small mobile */
    font-size: 10px;
    min-height: 28px;
    margin: 0 1px;
  }
`;

const TabIcon = styled.span`
  margin-right: 4px; /* Reduced margin */
  font-size: 12px; /* Smaller icon size */
`;



export interface TabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    {
      id: 'matrix' as TabType,
      label: 'X',
      icon: '📊'
    },
    {
      id: 'list' as TabType,
      label: 'List',
      icon: '📋'
    },
    {
      id: 'completed' as TabType,
      label: 'Done',
      icon: '✅'
    }
  ];

  return (
    <TabContainer>
      {tabs.map(tab => (
        <Tab
          key={tab.id}
          $active={activeTab === tab.id}
          onClick={() => onTabChange(tab.id)}
        >
          <TabIcon>{tab.icon}</TabIcon>
          {tab.label}
        </Tab>
      ))}
    </TabContainer>
  );
};

export default TabNavigation;
