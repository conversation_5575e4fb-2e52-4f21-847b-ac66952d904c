import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useF<PERSON>, Controller } from 'react-hook-form';
import type { NoteEditorModalProps, CreateNotePayload } from '../types';
import { getColorByPriority } from '../utils/noteUtils';

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.85); /* Increased opacity from 0.8 to 0.85 */
  backdrop-filter: blur(8px); /* Increased blur from 6px to 8px */
  display: ${props => props.$isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  /* Add subtle animation */
  animation: ${props => props.$isOpen ? 'fadeIn 0.3s ease-out' : 'none'};

  @keyframes fadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(8px);
    }
  }
`;

const ModalContent = styled.div`
  background: rgba(26, 27, 62, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 10px 20px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(148, 163, 184, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.4);
  
  /* Modern glass effect */
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%);
`;

const ModalHeader = styled.h2`
  color: #f8fafc;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #22d3ee 0%, #a855f7 50%, #f59e0b 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  color: #f8fafc; /* Increased contrast from #e2e8f0 to #f8fafc */
  font-weight: 500;
  font-size: 0.95rem;
  font-family: 'Poppins', sans-serif;
`;

const TextArea = styled.textarea`
  padding: 14px 16px;
  border: 1px solid rgba(148, 163, 184, 0.4); /* Increased border opacity */
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 0.95rem;
  resize: vertical;
  min-height: 120px;
  background: rgba(30, 41, 59, 0.9); /* Increased background opacity */
  color: #ffffff; /* Pure white for maximum contrast */
  transition: all 0.3s ease;

  &::placeholder {
    color: #cbd5e1; /* Lighter placeholder color for better visibility */
  }

  &:focus {
    border-color: #60a5fa;
    outline: none;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
    background: rgba(30, 41, 59, 0.95); /* Even more opaque on focus */
  }
`;

const Input = styled.input`
  padding: 14px 16px;
  border: 1px solid rgba(148, 163, 184, 0.4); /* Increased border opacity */
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 0.95rem;
  background: rgba(30, 41, 59, 0.9); /* Increased background opacity */
  color: #ffffff; /* Pure white for maximum contrast */
  transition: all 0.3s ease;

  &::placeholder {
    color: #cbd5e1; /* Lighter placeholder color for better visibility */
  }

  &:focus {
    border-color: #60a5fa;
    outline: none;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
    background: rgba(30, 41, 59, 0.95); /* Even more opaque on focus */
  }
`;

const DateTimeInput = styled(Input)`
  position: relative;
  font-family: 'Poppins', sans-serif;
  color: #f0f4ff;
  cursor: pointer;
  
  &::-webkit-calendar-picker-indicator {
    background: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f0f4ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e") no-repeat;
    background-size: 20px 20px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin-right: 8px;
    filter: drop-shadow(0 0 4px rgba(6, 182, 212, 0.3));
    transition: all 0.3s ease;
  }
  
  &:hover::-webkit-calendar-picker-indicator {
    filter: drop-shadow(0 0 8px rgba(6, 182, 212, 0.5));
    transform: scale(1.1);
  }
  
  &::-webkit-datetime-edit {
    color: #f0f4ff;
    padding: 0;
  }
  
  &::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  
  &::-webkit-datetime-edit-text {
    color: #c7d2fe;
    padding: 0 4px;
  }
  
  &::-webkit-datetime-edit-month-field,
  &::-webkit-datetime-edit-day-field,
  &::-webkit-datetime-edit-year-field,
  &::-webkit-datetime-edit-hour-field,
  &::-webkit-datetime-edit-minute-field,
  &::-webkit-datetime-edit-ampm-field {
    background: rgba(6, 182, 212, 0.1);
    color: #f0f4ff;
    padding: 2px 6px;
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.3s ease;
  }
  
  &::-webkit-datetime-edit-month-field:hover,
  &::-webkit-datetime-edit-day-field:hover,
  &::-webkit-datetime-edit-year-field:hover,
  &::-webkit-datetime-edit-hour-field:hover,
  &::-webkit-datetime-edit-minute-field:hover,
  &::-webkit-datetime-edit-ampm-field:hover {
    background: rgba(6, 182, 212, 0.2);
    color: #ffffff;
  }
  
  &:focus {
    border-color: #06b6d4;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
    
    &::-webkit-datetime-edit-month-field,
    &::-webkit-datetime-edit-day-field,
    &::-webkit-datetime-edit-year-field,
    &::-webkit-datetime-edit-hour-field,
    &::-webkit-datetime-edit-minute-field,
    &::-webkit-datetime-edit-ampm-field {
      background: rgba(6, 182, 212, 0.15);
    }
  }
`;

const RichTextEditor = styled.div`
  min-height: 120px;
  max-height: 200px;
  padding: 14px 16px;
  background: rgba(45, 27, 105, 0.8);
  color: #f0f4ff;
  font-family: 'Poppins', sans-serif;
  font-size: 0.95rem;
  line-height: 1.6;
  overflow-y: auto;
  border-radius: 12px;
  border: 1px solid rgba(99, 102, 241, 0.4);
  outline: none;
  cursor: text;
  transition: all 0.3s ease;
  
  &:empty::before {
    content: attr(data-placeholder);
    color: #c7d2fe;
    opacity: 0.7;
    pointer-events: none;
  }
  
  &:focus {
    border-color: #06b6d4;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
    background: rgba(45, 27, 105, 0.9);
  }
  
  /* Keep basic HTML formatting */
  strong { font-weight: 600; }
  em { font-style: italic; }
  u { text-decoration: underline; }
  h1, h2, h3 { font-weight: 600; margin: 8px 0 4px 0; }
  h1 { font-size: 1.4em; }
  h2 { font-size: 1.2em; }
  h3 { font-size: 1.1em; }
  p { margin: 4px 0; }
  ul, ol { margin: 8px 0; padding-left: 20px; }
  li { margin: 2px 0; }
  blockquote { 
    border-left: 3px solid rgba(6, 182, 212, 0.5);
    margin: 8px 0;
    padding-left: 12px;
    font-style: italic;
    opacity: 0.9;
  }
  a { color: #22d3ee; text-decoration: underline; }
`;

// Removed ColorPicker components - color now auto-determined by priority

const FormActions = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
  
  @media (max-width: 599px) {
    flex-direction: column;
    gap: 12px;
  }
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  flex: 1;
  min-width: 120px;
  padding: 14px 24px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 0.95rem;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  
  ${props => props.$variant === 'primary' ? `
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
    
    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.35);
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    &:disabled {
      background: rgba(148, 163, 184, 0.5);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  ` : `
    background: rgba(30, 41, 59, 0.9); /* More opaque background */
    color: #f1f5f9; /* Lighter text color for better contrast */
    border-color: rgba(148, 163, 184, 0.4); /* More visible border */

    &:hover {
      background: rgba(30, 41, 59, 0.95); /* Even more opaque on hover */
      border-color: rgba(148, 163, 184, 0.6); /* More visible border on hover */
      color: #ffffff; /* Pure white on hover */
    }
  `}
`;

const ErrorMessage = styled.span`
  color: #f87171;
  font-size: 0.875rem;
  font-weight: 400;
  font-family: 'Poppins', sans-serif;
`;

const Select = styled.select`
  padding: 14px 16px;
  border: 1px solid rgba(148, 163, 184, 0.4); /* Increased border opacity */
  border-radius: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 0.95rem;
  background: rgba(30, 41, 59, 0.9); /* Increased background opacity */
  color: #ffffff; /* Pure white for maximum contrast */
  transition: all 0.3s ease;
  cursor: pointer;

  &:focus {
    border-color: #60a5fa;
    outline: none;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
    background: rgba(30, 41, 59, 0.95); /* Even more opaque on focus */
  }

  option {
    background: rgba(30, 41, 59, 0.98); /* More opaque option background */
    color: #ffffff; /* Pure white text for options */
    padding: 8px;
  }
`;

const PriorityOption = styled.div<{ $priority: 'low' | 'medium' | 'high' | 'urgent' }>`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  backdrop-filter: blur(8px);
  font-weight: 500;
  
  ${props => {
    switch (props.$priority) {
      case 'urgent':
        return `
          background: linear-gradient(135deg, rgba(254, 226, 226, 0.9) 0%, rgba(254, 202, 202, 0.8) 100%);
          border: 2px solid rgba(220, 38, 38, 0.3);
          color: #7f1d1d;
          box-shadow: 
            0 4px 12px rgba(220, 38, 38, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        `;
      case 'high':
        return `
          background: linear-gradient(135deg, rgba(254, 215, 170, 0.9) 0%, rgba(253, 186, 116, 0.8) 100%);
          border: 2px solid rgba(249, 115, 22, 0.3);
          color: #9a3412;
          box-shadow: 
            0 4px 12px rgba(249, 115, 22, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        `;
      case 'medium':
        return `
          background: linear-gradient(135deg, rgba(224, 231, 255, 0.9) 0%, rgba(199, 210, 254, 0.8) 100%);
          border: 2px solid rgba(99, 102, 241, 0.3);
          color: #312e81;
          box-shadow: 
            0 4px 12px rgba(99, 102, 241, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        `;
      case 'low':
        return `
          background: linear-gradient(135deg, rgba(220, 252, 231, 0.9) 0%, rgba(187, 247, 208, 0.8) 100%);
          border: 2px solid rgba(34, 197, 94, 0.3);
          color: #14532d;
          box-shadow: 
            0 4px 12px rgba(34, 197, 94, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        `;
    }
  }}
  
  &:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: ${props => {
      switch (props.$priority) {
        case 'urgent': return '0 8px 25px rgba(220, 38, 38, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5)';
        case 'high': return '0 8px 25px rgba(249, 115, 22, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5)';
        case 'medium': return '0 8px 25px rgba(99, 102, 241, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5)';
        case 'low': return '0 8px 25px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5)';
      }
    }};
  }
  
  &:active {
    transform: translateY(0) scale(0.98);
  }
`;

const PriorityGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`;

const PriorityIndicator = styled.span<{ $priority: 'low' | 'medium' | 'high' | 'urgent' }>`
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  
  &::after {
    content: ${props => {
      switch (props.$priority) {
        case 'urgent': return "'⚡'";
        case 'high': return "'🔥'";
        case 'medium': return "'🔷'";
        case 'low': return "'✅'";
        default: return "'⭐'";
      }
    }};
  }
`;

const PriorityLabel = styled.span`
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Poppins', sans-serif;
`;


// Removed colorOptions - color now auto-determined by priority


const NoteEditorModal: React.FC<NoteEditorModalProps> = ({ 
  isOpen, 
  note, 
  onSave, 
  onClose 
}) => {
  const [selectedPriority, setSelectedPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  
  const { 
    register, 
    handleSubmit, 
    reset, 
    formState: { errors, isSubmitting },
    setValue,
    control
  } = useForm<CreateNotePayload>({
    defaultValues: {
      title: '',
      description: '',
      color: getColorByPriority('medium'), // Auto-set color based on priority
      quadrant: 'important_not_urgent',
      assigner: '',
      deadline: '',
      priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
      tags: []
    }
  });

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        event.preventDefault();
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  // Watch color changes (removed unused variable)

  useEffect(() => {
    if (isOpen) {
      if (note && note.id) {
        // Editing existing note
        setValue('title', note.title || '');
        setValue('description', note.description || '');
        setValue('color', getColorByPriority(note.priority || 'medium')); // Auto-set color based on priority
        setValue('quadrant', note.quadrant || 'important_not_urgent');
        setValue('assigner', note.assigner || '');
        setValue('deadline', note.deadline ?
          new Date(note.deadline).toISOString().slice(0, 16) : '');
        setValue('priority', note.priority || 'medium');
        setValue('tags', note.tags || []);
        setSelectedPriority(note.priority || 'medium');
      } else {
        // Creating new note - use quadrant from note if provided
        reset({
          title: '',
          description: '',
          color: getColorByPriority('medium'), // Auto-set color based on priority
          quadrant: note?.quadrant || 'important_not_urgent',
          assigner: '',
          deadline: '',
          priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
          tags: []
        });
        setSelectedPriority('medium');
      }
    }
  }, [isOpen, note, reset, setValue]);

  const onSubmit = (data: CreateNotePayload) => {
    // Convert empty strings to undefined for optional fields
    const cleanData: CreateNotePayload = {
      ...data,
      assigner: data.assigner?.trim() || undefined,
      deadline: data.deadline || undefined, // Keep as-is, no timezone conversion
      color: getColorByPriority(selectedPriority), // Auto-set color based on priority
      priority: selectedPriority
    };

    onSave(cleanData);
    handleClose();
  };



  const handleClose = () => {
    reset();
    setSelectedPriority('medium');
    onClose();
  };

  const handlePrioritySelect = (priority: 'low' | 'medium' | 'high' | 'urgent') => {
    setSelectedPriority(priority);
    setValue('priority', priority);
    setValue('color', getColorByPriority(priority)); // Auto-update color when priority changes
  };

  const priorityOptions: Array<{ value: 'low' | 'medium' | 'high' | 'urgent', label: string }> = [
    { value: 'urgent', label: 'Khẩn cấp' },
    { value: 'high', label: 'Cao' },
    { value: 'medium', label: 'Trung bình' },
    { value: 'low', label: 'Thấp' }
  ];

  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={(e) => {
      if (e.target === e.currentTarget) {
        handleClose();
      }
    }}>
      <ModalContent>
        <ModalHeader>
          {note && note.id ? 'Chỉnh sửa ghi chú' : 'Tạo ghi chú mới'}
        </ModalHeader>
        
        <Form onSubmit={handleSubmit(onSubmit)}>
          <FormGroup>
            <Label>Title *</Label>
            <Controller
              name="title"
              control={control}
              rules={{
                required: 'Title is required',
                validate: (value) => {
                  const textContent = (value || '').trim();
                  if (!textContent) return 'Title is required';
                  if (textContent.length > 300) return 'Title cannot exceed 300 characters';
                  return true;
                }
              }}
              render={({ field: { onChange, value } }) => (
                <TextArea
                  value={value || ''}
                  onChange={(e) => onChange(e.target.value)}
                  placeholder="Enter note title..."
                  rows={2}
                />
              )}
            />
            {errors.title && <ErrorMessage>{errors.title.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label>Mô tả chi tiết</Label>
            <Controller
              name="description"
              control={control}
              rules={{
                validate: (value) => {
                  const textContent = (value || '').trim();
                  if (textContent.length > 1000) return 'Mô tả không được vượt quá 1000 ký tự';
                  return true;
                }
              }}
              render={({ field: { onChange, value } }) => (
                <TextArea
                  value={value || ''}
                  onChange={(e) => onChange(e.target.value)}
                  placeholder="Nhập mô tả chi tiết (tùy chọn)..."
                  rows={4}
                />
              )}
            />
            {errors.description && <ErrorMessage>{errors.description.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label>Category</Label>
            <Controller
              name="quadrant"
              control={control}
              render={({ field: { onChange, value } }) => (
                <select
                  value={value || 'important_not_urgent'}
                  onChange={(e) => onChange(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    borderRadius: '8px',
                    border: '1px solid rgba(148, 163, 184, 0.3)',
                    background: 'rgba(30, 41, 59, 0.8)',
                    color: '#f8fafc',
                    fontSize: '14px'
                  }}
                >
                  <option value="important_urgent">🚨 Việc gấp rút (Quan trọng & Khẩn cấp)</option>
                  <option value="important_not_urgent">📅 Lên lịch thực hiện (Quan trọng & Không khẩn cấp)</option>
                  <option value="not_important_urgent">👥 Việc có thể ủy thác (Không quan trọng & Khẩn cấp)</option>
                  <option value="not_important_not_urgent">🗑️ Loại bỏ (Không quan trọng & Không khẩn cấp)</option>
                </select>
              )}
            />
          </FormGroup>

          {/* Removed color picker - color now auto-determined by priority */}

          {/* Priority selection removed as requested */}

          <FormGroup>
            <Label>Người giao</Label>
            <Input
              {...register('assigner', { 
                maxLength: { value: 100, message: 'Tên người giao không được vượt quá 100 ký tự' }
              })}
              placeholder="Tên người giao (tùy chọn)"
            />
            {errors.assigner && <ErrorMessage>{errors.assigner.message}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label>Hạn chót</Label>
            <DateTimeInput
              type="datetime-local"
              {...register('deadline', {
                validate: (value) => {
                  if (!value) return true;
                  const selectedDate = new Date(value);
                  const now = new Date();
                  return selectedDate > now || 'Hạn chót phải là thời gian trong tương lai';
                }
              })}
            />
            {errors.deadline && <ErrorMessage>{errors.deadline.message}</ErrorMessage>}
          </FormGroup>

          <FormActions>
            <Button type="button" $variant="secondary" onClick={handleClose}>
              Hủy
            </Button>
            <Button 
              type="submit" 
              $variant="primary" 
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Đang lưu...' : (note && note.id ? 'Cập nhật' : 'Tạo mới')}
            </Button>
          </FormActions>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default NoteEditorModal;