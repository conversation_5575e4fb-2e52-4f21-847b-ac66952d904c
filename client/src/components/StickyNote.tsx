import React, { memo } from 'react';
import styled from 'styled-components';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { StickyNoteProps } from '../types';

const NoteContainer = styled.div<{
  $backgroundColor: string;
  $isDragging: boolean;
  $transform: string;
  $transition: string;
  // Removed selection props to avoid conflict with drag & drop
}>`
  position: relative;
  width: 100%; /* Full width within column */
  min-width: 120px; /* Smaller minimum width */
  max-width: 100%; /* Full width of column */
  min-height: 80px; /* Reduced height for compact layout */
  background: ${props => props.$backgroundColor};
  background-image: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.03) 100%);
  border-radius: 6px; /* Smaller border radius */
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.08),
    0 4px 8px rgba(0, 0, 0, 0.12);
  margin: 0 0 6px 0; /* Small bottom margin */
  padding: 10px; /* Reduced padding */
  font-family: 'Poppins', sans-serif;
  cursor: grab;
  transform: ${props => props.$isDragging ? `${props.$transform} rotate(3deg) scale(1.03)` : props.$transform};
  transition: ${props => props.$transition || 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)'};
  z-index: ${props => props.$isDragging ? 1000 : 1};
  opacity: ${props => props.$isDragging ? 0.8 : 1};
  filter: ${props => props.$isDragging ? 'drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3))' : 'none'};
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;

  &:hover {
    ${props => !props.$isDragging && `
      box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.15),
        0 9px 18px rgba(0, 0, 0, 0.18),
        0 12px 24px rgba(0, 0, 0, 0.22);
      transform: ${props.$transform} translateY(-3px) scale(1.02);
      filter: brightness(1.05);
    `}
  }

  &:active {
    cursor: grabbing;
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    width: 200px;
    min-width: 180px;
    max-width: 220px;
    min-height: 100px;
  }

  @media (max-width: 768px) {
    width: 100%; /* Full width on mobile */
    min-width: auto;
    max-width: 100%;
    min-height: 95px;
    padding: 12px;
    border-radius: 8px;
  }
`;

const NoteTitle = styled.h3`
  font-size: 13px; /* Even smaller for compact view */
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0; /* Further reduced margin */
  line-height: 1.2; /* Tighter line height */
  word-wrap: break-word;
  font-family: 'Poppins', sans-serif;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Limit to 2 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  @media (max-width: 768px) {
    font-size: 12px;
    -webkit-line-clamp: 2; /* Keep 2 lines on mobile too */
  }
`;

const ContentArea = styled.div`
  font-size: 14px;
  line-height: 22px;
  color: #4b5563;
  margin-bottom: 16px;
  word-wrap: break-word;
  min-height: 50px;
  font-weight: 400;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 0.01em;
  white-space: pre-wrap; /* Preserve whitespace and line breaks */
  overflow-wrap: break-word; /* Handle long words */
  
  /* Styles for rich text content */
  h1, h2, h3 {
    margin: 8px 0 4px 0;
    font-weight: 600;
    line-height: 1.2;
  }
  
  h1 { font-size: 1.4em; }
  h2 { font-size: 1.2em; }
  h3 { font-size: 1.1em; }
  
  p {
    margin: 4px 0;
  }
  
  strong {
    font-weight: 600;
  }
  
  em {
    font-style: italic;
  }
  
  u {
    text-decoration: underline;
  }
  
  s {
    text-decoration: line-through;
  }
  
  sup {
    vertical-align: super;
    font-size: 0.8em;
  }
  
  sub {
    vertical-align: sub;
    font-size: 0.8em;
  }
  
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;
  }
  
  ul li {
    list-style-type: disc;
  }
  
  ol li {
    list-style-type: decimal;
  }
  
  li {
    margin: 2px 0;
  }
  
  blockquote {
    border-left: 3px solid rgba(0, 0, 0, 0.2);
    margin: 8px 0;
    padding-left: 12px;
    font-style: italic;
    opacity: 0.8;
  }
  
  code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
  }
  
  pre {
    background: rgba(0, 0, 0, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    overflow-x: auto;
  }
  
  a {
    color: #2563eb;
    text-decoration: underline;
  }
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 4px 0;
  }
  
  /* Text alignment */
  .ql-align-center {
    text-align: center;
  }
  
  .ql-align-right {
    text-align: right;
  }
  
  .ql-align-justify {
    text-align: justify;
  }
`;

const MetadataArea = styled.div`
  font-size: 12px;
  color: #475569;
  line-height: 1.4;
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const MetadataItem = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0;
`;

const ActionsArea = styled.div<{ $isDragging?: boolean }>`
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 6px; /* Gap for 3 buttons */
  opacity: ${props => props.$isDragging ? '0' : '0'}; /* Hidden by default */
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: 15; /* Higher z-index to ensure it's above drag elements */
  pointer-events: ${props => props.$isDragging ? 'none' : 'auto'};
  flex-wrap: wrap; /* Allow wrapping if needed on smaller screens */
  max-width: 110px; /* Limit width to prevent overflow */
  transform: translateY(-2px);

  /* Show on hover */
  ${NoteContainer}:hover & {
    opacity: ${props => props.$isDragging ? '0' : '1'};
    transform: translateY(0);
    pointer-events: auto; /* Ensure pointer events work on hover */
  }

  /* Responsive design for smaller screens */
  @media (max-width: 768px) {
    gap: 4px;
    max-width: 100px;
  }
`;

const ActionButton = styled.button<{ $variant: 'edit' | 'delete' | 'complete' }>`
  width: 28px; /* Slightly smaller to fit 3 buttons */
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px; /* Slightly smaller font */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  ${props => {
    switch (props.$variant) {
      case 'edit':
        return `
          background: rgba(6, 182, 212, 0.9);
          color: white;

          &:hover {
            background: rgba(8, 145, 178, 1);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 12px rgba(6, 182, 212, 0.5);
          }
        `;
      case 'delete':
        return `
          background: rgba(245, 158, 11, 0.9);
          color: white;

          &:hover {
            background: rgba(217, 119, 6, 1);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.5);
          }
        `;
      case 'complete':
        return `
          background: rgba(34, 197, 94, 0.9);
          color: white;

          &:hover {
            background: rgba(22, 163, 74, 1);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.5);
          }
        `;
      default:
        return '';
    }
  }}
  
  &:active {
    transform: translateY(0) scale(0.95);
  }
`;

const CompletionIcon = styled.div<{ $completed: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
  cursor: pointer;

  ${props => props.$completed ? `
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    box-shadow:
      0 4px 12px rgba(34, 197, 94, 0.4),
      0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    &::before {
      content: '✓';
      font-size: 16px;
      font-weight: 900;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    &:hover {
      transform: scale(1.1);
      box-shadow:
        0 6px 16px rgba(34, 197, 94, 0.5),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
  ` : `
    background: rgba(156, 163, 175, 0.2);
    border: 2px solid rgba(156, 163, 175, 0.4);
    color: rgba(156, 163, 175, 0.6);

    &:hover {
      border-color: rgba(34, 197, 94, 0.6);
      background: rgba(34, 197, 94, 0.1);
      transform: scale(1.05);
    }
  `}
`;

// Removed checkbox components to avoid conflict with drag & drop

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
`;

const Tag = styled.span`
  background: rgba(99, 102, 241, 0.15);
  color: #6366f1;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid rgba(99, 102, 241, 0.3);
`;

const PriorityBadge = styled.span<{ $priority: 'low' | 'medium' | 'high' | 'urgent' }>`
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  ${props => {
    switch (props.$priority) {
      case 'urgent':
        return `
          background: linear-gradient(135deg, #fee2e2 0%, #fecaca 50%, #fca5a5 100%);
          color: #7f1d1d;
          border: 1px solid rgba(220, 38, 38, 0.4);
          box-shadow: 
            0 4px 12px rgba(220, 38, 38, 0.25),
            0 2px 4px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
            border-radius: 20px;
            z-index: -1;
          }
          
          &:hover {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 
              0 8px 25px rgba(220, 38, 38, 0.35),
              0 4px 8px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.4);
          }
        `;
      case 'high':
        return `
          background: linear-gradient(135deg, #fed7aa 0%, #fdba74 50%, #fb923c 100%);
          color: #9a3412;
          border: 1px solid rgba(249, 115, 22, 0.4);
          box-shadow: 
            0 4px 12px rgba(249, 115, 22, 0.25),
            0 2px 4px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(249, 115, 22, 0.05) 100%);
            border-radius: 20px;
            z-index: -1;
          }
          
          &:hover {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 
              0 8px 25px rgba(249, 115, 22, 0.35),
              0 4px 8px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.4);
          }
        `;
      case 'medium':
        return `
          background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 50%, #a5b4fc 100%);
          color: #312e81;
          border: 1px solid rgba(99, 102, 241, 0.4);
          box-shadow: 
            0 4px 12px rgba(99, 102, 241, 0.25),
            0 2px 4px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(165, 180, 252, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
            border-radius: 20px;
            z-index: -1;
          }
          
          &:hover {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 
              0 8px 25px rgba(99, 102, 241, 0.35),
              0 4px 8px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.4);
          }
        `;
      case 'low':
        return `
          background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 50%, #86efac 100%);
          color: #14532d;
          border: 1px solid rgba(34, 197, 94, 0.4);
          box-shadow: 
            0 4px 12px rgba(34, 197, 94, 0.25),
            0 2px 4px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
          
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(134, 239, 172, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
            border-radius: 20px;
            z-index: -1;
          }
          
          &:hover {
            transform: translateY(-1px) scale(1.02);
            box-shadow: 
              0 8px 25px rgba(34, 197, 94, 0.35),
              0 4px 8px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.4);
          }
        `;
      default:
        return `
          background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 50%, #d1d5db 100%);
          color: #374151;
          border: 1px solid rgba(107, 114, 128, 0.4);
          box-shadow: 
            0 4px 12px rgba(107, 114, 128, 0.15),
            0 2px 4px rgba(0, 0, 0, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        `;
    }
  }}
`;

const MetadataRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
`;

const AssignerInfo = styled.div`
  font-size: 12px;
  color: rgba(31, 41, 55, 0.7);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(99, 102, 241, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
`;

const DeadlineInfo = styled.div<{ $isOverdue?: boolean }>`
  font-size: 12px;
  color: ${props => props.$isOverdue ? '#ef4444' : 'rgba(31, 41, 55, 0.7)'};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  background: ${props => props.$isOverdue ? 'rgba(239, 68, 68, 0.1)' : 'rgba(107, 114, 128, 0.1)'};
  padding: 4px 8px;
  border-radius: 8px;
`;





const CreationDate = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  font-size: 9px; /* Smaller font */
  color: #52525b;
  font-weight: 500;
  padding: 6px 8px; /* Reduced padding */
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 0 0 6px 6px; /* Smaller border radius */
  backdrop-filter: blur(8px);
  font-family: 'Poppins', sans-serif;
  letter-spacing: 0.2px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  }
`;



const DynamicCalendarIcon = styled.span<{ $day: string }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 20px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 9px;
  font-weight: 700;
  color: #374151;
  margin-right: 6px;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  position: relative;
  font-family: 'Poppins', sans-serif;
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 4px;
    right: 4px;
    height: 3px;
    background: #ef4444;
    border-radius: 3px 3px 0 0;
  }
  
  &::after {
    content: '${props => props.$day}';
    line-height: 1;
    margin-top: 1px;
  }
`;

const StickyNote: React.FC<StickyNoteProps> = ({
  note,
  onEdit,
  onDelete,
  onToggleComplete
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: note.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch {
      return '';
    }
  };

  const getDayFromDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.getDate().toString();
    } catch {
      return '📅';
    }
  };

  const handleToggleComplete = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering drag or edit
    e.preventDefault(); // Prevent default behavior
    if (onToggleComplete) {
      onToggleComplete(note); // Pass the original note, let MainApp handle the toggle logic
    }
  };

  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);

      // Use UTC methods to avoid timezone conversion
      const year = date.getUTCFullYear();
      const month = date.getUTCMonth() + 1;
      const day = date.getUTCDate();
      const hour = date.getUTCHours();
      const minute = date.getUTCMinutes();

      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const targetDate = new Date(year, month - 1, day);

      // Format time
      const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

      // If same day, only show time
      if (targetDate.getTime() === today.getTime()) {
        return timeStr;
      }

      // If this year, show dd/mm + time
      if (year === now.getFullYear()) {
        return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}, ${timeStr}`;
      }

      // Different year, show full date + time
      const shortYear = year.toString().slice(-2);
      return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${shortYear}, ${timeStr}`;
    } catch {
      return '';
    }
  };

  const isOverdue = note.deadline && new Date(note.deadline) < new Date();

  return (
    <NoteContainer
      ref={setNodeRef}
      style={style}
      className={`sticky-note ${isDragging ? 'dragging dnd-kit-draggable' : ''}`}
      $backgroundColor={note.color}
      $isDragging={isDragging}
      $transform={CSS.Transform.toString(transform) || ''}
      $transition={transition || ''}
    >


      <div {...attributes} {...listeners} style={{ cursor: isDragging ? 'grabbing' : 'grab', paddingBottom: '25px' }}>
        {/* Title section - prominently displayed as main content */}
        <NoteTitle>{note.title || 'Không có tiêu đề'}</NoteTitle>

        {/* Hide description for compact view - only show title */}
        {/* Description is hidden to create cleaner, more compact view */}

        {/* Tags section - only show if present and keep compact */}
        {note.tags && note.tags.length > 0 && (
          <TagsContainer>
            {note.tags.slice(0, 2).map((tag, index) => ( /* Limit to 2 tags for compact view */
              <Tag key={index}>{tag}</Tag>
            ))}
            {note.tags.length > 2 && (
              <Tag>+{note.tags.length - 2}</Tag>
            )}
          </TagsContainer>
        )}

        {/* Compact metadata row - only show most important info */}
        {(note.assigner || note.deadline) && (
          <MetadataRow>
            <div style={{ display: 'flex', gap: '6px', alignItems: 'center', width: '100%', justifyContent: 'flex-end', fontSize: '11px' }}>
              {note.deadline && (
                <DeadlineInfo $isOverdue={isOverdue} style={{ fontSize: '11px', padding: '2px 6px' }}>
                  ⏰ {formatDateTime(note.deadline)}
                  {isOverdue && ' ⚠️'}
                </DeadlineInfo>
              )}

              {note.assigner && (
                <AssignerInfo style={{ fontSize: '11px', padding: '2px 6px' }}>
                  👤 {note.assigner}
                </AssignerInfo>
              )}
            </div>
          </MetadataRow>
        )}
      </div>

      <ActionsArea $isDragging={isDragging}>
        <ActionButton
          $variant="complete"
          onClick={handleToggleComplete}
          onPointerDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          title={note.completed ? 'Mark as incomplete' : 'Mark as complete'}
          style={{
            background: note.completed
              ? 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)'
              : 'rgba(156, 163, 175, 0.3)',
            border: note.completed
              ? 'none'
              : '2px solid rgba(156, 163, 175, 0.6)',
            borderRadius: '50%',
            width: '26px',
            height: '26px',
            color: note.completed ? 'white' : 'rgba(156, 163, 175, 0.8)',
            fontSize: '14px',
            fontWeight: 'bold',
            boxShadow: note.completed
              ? '0 2px 8px rgba(34, 197, 94, 0.3)'
              : '0 1px 3px rgba(0, 0, 0, 0.1)',
            pointerEvents: 'auto',
            zIndex: 20
          }}
        >
          {note.completed ? '✓' : '○'}
        </ActionButton>
        <ActionButton
          $variant="edit"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            onEdit(note);
          }}
          onPointerDown={(e) => e.stopPropagation()}
          title="Chỉnh sửa ghi chú"
        >
          ✏️
        </ActionButton>
        <ActionButton
          $variant="delete"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            if (window.confirm('Bạn có chắc chắn muốn xóa ghi chú này?')) {
              onDelete(note.id);
            }
          }}
          onPointerDown={(e) => e.stopPropagation()}
          title="Xóa ghi chú"
        >
          🗑️
        </ActionButton>
      </ActionsArea>
      
      <CreationDate style={{ padding: '8px 12px', fontSize: '10px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%' }}>
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <DynamicCalendarIcon $day={getDayFromDate(note.createdAt)} style={{ width: '20px', height: '16px', fontSize: '8px' }} />
            {formatDate(note.createdAt)}
          </span>
          {/* Priority badges removed for cleaner appearance */}
        </div>
      </CreationDate>
    </NoteContainer>
  );
};

// Memoize component to prevent unnecessary re-renders
export default memo(StickyNote, (prevProps, nextProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.note.id === nextProps.note.id &&
    prevProps.note.content === nextProps.note.content &&
    prevProps.note.title === nextProps.note.title &&
    prevProps.note.priority === nextProps.note.priority &&
    prevProps.note.color === nextProps.note.color &&
    prevProps.note.status === nextProps.note.status &&
    prevProps.note.updatedAt === nextProps.note.updatedAt
  );
});