import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import type { Status } from '../types';

const QuickAddContainer = styled.div<{ $isActive: boolean }>`
  margin-bottom: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: ${props => props.$isActive ? 1 : 0.8};
`;

const QuickAddInput = styled.textarea<{ $isActive: boolean }>`
  width: 100%;
  min-height: ${props => props.$isActive ? '80px' : '40px'};
  padding: 12px 16px;
  border: 2px solid ${props => props.$isActive ? 'rgba(99, 102, 241, 0.6)' : 'rgba(99, 102, 241, 0.3)'};
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 0.9rem;
  font-family: 'Poppins', sans-serif;
  resize: none;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
  
  &:focus {
    border-color: rgba(6, 182, 212, 0.8);
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.2);
    background: rgba(255, 255, 255, 0.08);
  }
`;

const QuickAddActions = styled.div<{ $show: boolean }>`
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: ${props => props.$show ? 1 : 0};
  transform: ${props => props.$show ? 'translateY(0)' : 'translateY(-10px)'};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: ${props => props.$show ? 'auto' : 'none'};
`;

const QuickButton = styled.button<{ $variant: 'primary' | 'secondary' }>`
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.$variant === 'primary' ? `
    background: linear-gradient(135deg, #06b6d4 0%, #8b5cf6 100%);
    color: white;
    
    &:hover {
      background: linear-gradient(135deg, #0891b2 0%, #7c3aed 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
    }
  ` : `
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: white;
    }
  `}
  
  &:active {
    transform: scale(0.95);
  }
`;

interface QuickAddNoteProps {
  status: Status;
  onCreateNote: (content: string, status: Status) => void;
}

const QuickAddNote: React.FC<QuickAddNoteProps> = ({ status, onCreateNote }) => {
  const [isActive, setIsActive] = useState(false);
  const [content, setContent] = useState('');
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isActive && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isActive]);

  const handleSubmit = () => {
    if (content.trim()) {
      onCreateNote(content.trim(), status);
      setContent('');
      setIsActive(false);
    }
  };

  const handleCancel = () => {
    setContent('');
    setIsActive(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (!isActive) {
    return (
      <QuickAddContainer $isActive={false}>
        <QuickAddInput
          $isActive={false}
          placeholder="➕ Nhấp để thêm ghi chú nhanh..."
          value=""
          readOnly
          onClick={() => setIsActive(true)}
          style={{ cursor: 'pointer' }}
        />
      </QuickAddContainer>
    );
  }

  return (
    <QuickAddContainer $isActive={true}>
      <QuickAddInput
        ref={inputRef}
        $isActive={true}
        placeholder="Nhập nội dung ghi chú... (Ctrl+Enter để lưu, Esc để hủy)"
        value={content}
        onChange={(e) => setContent(e.target.value)}
        onKeyDown={handleKeyDown}
        onBlur={(e) => {
          // Only close if clicking outside and not on action buttons
          if (!e.relatedTarget?.closest('[data-quick-actions]')) {
            setTimeout(() => setIsActive(false), 100);
          }
        }}
      />
      <QuickAddActions $show={true} data-quick-actions>
        <QuickButton $variant="primary" onClick={handleSubmit}>
          Thêm
        </QuickButton>
        <QuickButton $variant="secondary" onClick={handleCancel}>
          Hủy
        </QuickButton>
      </QuickAddActions>
    </QuickAddContainer>
  );
};

export default QuickAddNote;
