import React, { useState, useMemo } from 'react';
import styled from 'styled-components';
import type { StickyNote as StickyNoteType, Quadrant } from '../types';

const Container = styled.div`
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  padding: 24px;
  min-height: calc(100vh - 200px);
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
`;

const Title = styled.h2`
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
`;

const SearchInput = styled.input`
  padding: 8px 12px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  min-width: 200px;
  
  &:focus {
    outline: none;
    border-color: #28a745;
  }
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #28a745;
  }
`;

const ProjectGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
`;

const ProjectCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #28a745;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const CompletedBadge = styled.div`
  position: absolute;
  top: 16px;
  right: 16px;
  background: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const ProjectTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-right: 80px; // Space for the completed badge
`;

const ProjectDescription = styled.p`
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const ProjectMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
`;

const MetaBadge = styled.span<{ $color: string }>`
  background: ${props => props.$color};
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
`;

const ProjectFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
`;

const DateInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
  }
  
  &.restore {
    border-color: #28a745;
    color: #28a745;
    
    &:hover {
      background: #28a745;
      color: white;
    }
  }
  
  &.delete {
    border-color: #dc3545;
    color: #dc3545;
    
    &:hover {
      background: #dc3545;
      color: white;
    }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const StatCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const StatNumber = styled.div`
  font-size: 24px;
  font-weight: 700;
  color: #28a745;
  margin-bottom: 4px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 600;
`;

interface CompletedProjectsViewProps {
  notes: StickyNoteType[];
  onNoteEdit: (note: StickyNoteType) => void;
  onNoteDelete: (id: string) => void;
  onToggleComplete?: (note: StickyNoteType) => void;
}

const CompletedProjectsView: React.FC<CompletedProjectsViewProps> = ({
  notes,
  onNoteEdit,
  onNoteDelete,
  onToggleComplete
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('completed');

  // Filter only completed notes based on the completed field
  const completedNotes = useMemo(() => {
    return notes.filter(note => note.completed === true);
  }, [notes]);

  const filteredAndSortedNotes = useMemo(() => {
    let filtered = completedNotes.filter(note => {
      const matchesSearch = !searchTerm || 
        note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSearch;
    });

    // Sort notes
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'completed':
        default:
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      }
    });

    return filtered;
  }, [completedNotes, searchTerm, sortBy]);

  const stats = useMemo(() => {
    const total = completedNotes.length;
    const thisMonth = completedNotes.filter(note => {
      const noteDate = new Date(note.updatedAt);
      const now = new Date();
      return noteDate.getMonth() === now.getMonth() && noteDate.getFullYear() === now.getFullYear();
    }).length;
    
    const thisWeek = completedNotes.filter(note => {
      const noteDate = new Date(note.updatedAt);
      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      return noteDate >= weekAgo;
    }).length;

    return { total, thisMonth, thisWeek };
  }, [completedNotes]);

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleRestore = (note: StickyNoteType) => {
    // Move back to important_not_urgent quadrant and mark as incomplete
    onNoteEdit({
      ...note,
      quadrant: 'important_not_urgent',
      completed: false,
      tags: note.tags.filter(tag => tag !== 'completed')
    });
  };

  return (
    <Container>
      <Header>
        <Title>
          ✅ Dự án đã hoàn thành
          <span style={{ fontSize: '16px', fontWeight: 'normal', color: '#666' }}>
            ({filteredAndSortedNotes.length} dự án)
          </span>
        </Title>
        
        <FilterContainer>
          <SearchInput
            type="text"
            placeholder="Tìm kiếm dự án đã hoàn thành..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          
          <FilterSelect
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="completed">Ngày hoàn thành</option>
            <option value="created">Ngày tạo</option>
            <option value="title">Tên dự án</option>
          </FilterSelect>
        </FilterContainer>
      </Header>

      <StatsContainer>
        <StatCard>
          <StatNumber>{stats.total}</StatNumber>
          <StatLabel>Tổng số dự án hoàn thành</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.thisMonth}</StatNumber>
          <StatLabel>Hoàn thành tháng này</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.thisWeek}</StatNumber>
          <StatLabel>Hoàn thành tuần này</StatLabel>
        </StatCard>
      </StatsContainer>

      {filteredAndSortedNotes.length === 0 ? (
        <EmptyState>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎉</div>
          <h3>Chưa có dự án nào hoàn thành</h3>
          <p>Hoàn thành các task để thấy chúng xuất hiện ở đây</p>
        </EmptyState>
      ) : (
        <ProjectGrid>
          {filteredAndSortedNotes.map(note => (
            <ProjectCard key={note.id}>
              <CompletedBadge>
                ✅ Hoàn thành
              </CompletedBadge>
              
              <ProjectTitle>{note.title || 'Không có tiêu đề'}</ProjectTitle>
              
              {note.description && (
                <ProjectDescription>{note.description}</ProjectDescription>
              )}
              
              <ProjectMeta>
                <MetaBadge $color="#28a745">
                  {note.priority || 'medium'}
                </MetaBadge>
                {note.assigner && (
                  <MetaBadge $color="#6c757d">
                    {note.assigner}
                  </MetaBadge>
                )}
                {note.tags.filter(tag => tag !== 'completed').map(tag => (
                  <MetaBadge key={tag} $color="#17a2b8">
                    {tag}
                  </MetaBadge>
                ))}
              </ProjectMeta>
              
              <ProjectFooter>
                <DateInfo>
                  <div>Tạo: {formatDate(note.createdAt)}</div>
                  <div>Hoàn thành: {formatDate(note.updatedAt)}</div>
                </DateInfo>
                
                <ActionButtons>
                  {onToggleComplete && (
                    <ActionButton
                      className="incomplete"
                      onClick={() => onToggleComplete(note)}
                      title="Mark as incomplete"
                      style={{
                        background: 'rgba(239, 68, 68, 0.1)',
                        color: '#dc2626',
                        border: '1px solid rgba(239, 68, 68, 0.3)'
                      }}
                    >
                      ❌ Incomplete
                    </ActionButton>
                  )}
                  <ActionButton
                    className="restore"
                    onClick={() => handleRestore(note)}
                    title="Khôi phục dự án"
                  >
                    ↩️ Khôi phục
                  </ActionButton>
                  <ActionButton
                    className="delete"
                    onClick={() => onNoteDelete(note.id)}
                    title="Xóa vĩnh viễn"
                  >
                    🗑️ Xóa
                  </ActionButton>
                </ActionButtons>
              </ProjectFooter>
            </ProjectCard>
          ))}
        </ProjectGrid>
      )}
    </Container>
  );
};

export default CompletedProjectsView;
