declare module 'react-quill' {
  import { Component } from 'react';

  interface QuillModules {
    toolbar?: any;
    [key: string]: any;
  }

  interface ReactQuillProps {
    theme?: string;
    value?: string;
    defaultValue?: string;
    placeholder?: string;
    readOnly?: boolean;
    modules?: QuillModules;
    formats?: string[];
    style?: React.CSSProperties;
    className?: string;
    onChange?: (content: string, delta: any, source: string, editor: any) => void;
    onBlur?: (range: any, source: string, editor: any) => void;
    onFocus?: (range: any, source: string, editor: any) => void;
    onKeyDown?: React.KeyboardEventHandler<any>;
    onKeyPress?: React.KeyboardEventHandler<any>;
    onKeyUp?: React.KeyboardEventHandler<any>;
    tabIndex?: number;
    bounds?: string | HTMLElement;
    scrollingContainer?: string | HTMLElement;
    children?: React.ReactNode;
  }

  class ReactQuill extends Component<ReactQuillProps> {
    constructor(props: ReactQuillProps);
    focus(): void;
    blur(): void;
    getEditor(): any;
    getEditingArea(): HTMLElement;
  }

  export = ReactQuill;
}