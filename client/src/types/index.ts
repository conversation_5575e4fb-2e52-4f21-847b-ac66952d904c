export type TabType = 'matrix' | 'list' | 'completed';

export interface StickyNote {
  id: string;
  description: string;
  title: string;
  color: string;
  quadrant: 'important_urgent' | 'important_not_urgent' | 'not_important_urgent' | 'not_important_not_urgent';
  order: number;
  createdAt: string;
  assigner?: string;
  deadline?: string;
  updatedAt: string;
  tags: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  completed?: boolean;
  columnIndex?: number; // Add column index for tracking which column the note is in
}

export interface CreateNotePayload {
  description: string;
  title?: string;
  color?: string;
  quadrant?: 'important_urgent' | 'important_not_urgent' | 'not_important_urgent' | 'not_important_not_urgent';
  assigner?: string;
  deadline?: string;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  completed?: boolean;
}

export interface UpdateNotePayload {
  description?: string;
  title?: string;
  color?: string;
  quadrant?: 'important_urgent' | 'important_not_urgent' | 'not_important_urgent' | 'not_important_not_urgent';
  order?: number;
  assigner?: string;
  deadline?: string;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export interface ReorderUpdate {
  id: string;
  quadrant?: 'important_urgent' | 'important_not_urgent' | 'not_important_urgent' | 'not_important_not_urgent';
  order?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  count?: number;
  message?: string;
}

export type Quadrant = 'important_urgent' | 'important_not_urgent' | 'not_important_urgent' | 'not_important_not_urgent';

export interface QuadrantProps {
  quadrant: Quadrant;
  notes: StickyNote[];
  onNoteEdit: (note: StickyNote) => void;
  onNoteDelete: (id: string) => void;
  onCreateNote?: (quadrant: Quadrant) => void;
  onQuickCreateNote?: (description: string, quadrant: Quadrant) => void;
}

export interface TabViewProps {
  activeTab: 'matrix' | 'list' | 'completed';
  onTabChange: (tab: 'matrix' | 'list' | 'completed') => void;
}

export interface EisenhowerMatrixProps {
  notes: StickyNote[];
  onNoteEdit: (note: StickyNote) => void; // For drag-and-drop updates
  onNoteClick?: (note: StickyNote) => void; // For click-to-edit functionality
  onNoteDelete: (id: string) => void;
  onCreateNote?: (quadrant: Quadrant) => void;
  onQuickCreateNote?: (description: string, quadrant: Quadrant) => void;
  onToggleComplete?: (note: StickyNote) => void;
}

// Legacy types for backward compatibility
export type Status = 'todo' | 'processing' | 'done';

export interface ColumnProps {
  status: Status;
  notes: StickyNote[];
  onNoteEdit: (note: StickyNote) => void;
  onNoteDelete: (id: string) => void;
  onCreateNote?: (status: Status) => void;
  onQuickCreateNote?: (content: string, status: Status) => void;
}

export interface StickyNoteProps {
  note: StickyNote;
  onEdit: (note: StickyNote) => void;
  onDelete: (id: string) => void;
  onToggleComplete?: (note: StickyNote) => void;
}

export interface NoteEditorModalProps {
  isOpen: boolean;
  note: StickyNote | null;
  onSave: (data: CreateNotePayload) => void;
  onClose: () => void;
}