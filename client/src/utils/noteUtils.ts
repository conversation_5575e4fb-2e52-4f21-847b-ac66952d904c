import type { StickyNote } from '../types';

// Priority order mapping for efficient sorting
const PRIORITY_ORDER = { urgent: 0, high: 1, medium: 2, low: 3 } as const;

/**
 * Efficiently sort notes by priority (urgent > high > medium > low)
 * Uses memoized priority values for better performance
 */
export const sortNotesByPriority = (notes: StickyNote[]): StickyNote[] => {
  return notes.sort((a, b) => {
    const aPriority = PRIORITY_ORDER[a.priority as keyof typeof PRIORITY_ORDER] ?? 2;
    const bPriority = PRIORITY_ORDER[b.priority as keyof typeof PRIORITY_ORDER] ?? 2;
    return aPriority - bPriority;
  });
};

/**
 * Add a new note to the list and maintain priority order
 */
export const addNoteWithPrioritySort = (notes: StickyNote[], newNote: StickyNote): StickyNote[] => {
  const updatedNotes = [...notes, newNote];
  return sortNotesByPriority(updatedNotes);
};

/**
 * Update a note in the list and maintain priority order
 */
export const updateNoteWithPrioritySort = (
  notes: StickyNote[], 
  noteId: string, 
  updatedNote: StickyNote
): StickyNote[] => {
  const updatedNotes = notes.map(note => note.id === noteId ? updatedNote : note);
  return sortNotesByPriority(updatedNotes);
};

/**
 * Remove a note from the list
 */
export const removeNoteFromList = (notes: StickyNote[], noteId: string): StickyNote[] => {
  return notes.filter(note => note.id !== noteId);
};


/**
 * Get color based on priority level
 */
export const getColorByPriority = (priority: 'low' | 'medium' | 'high' | 'urgent'): string => {
  const priorityColors = {
    urgent: '#fecaca',   // Red 200 - Khẩn cấp
    high: '#fed7aa',     // Orange 200 - Cao
    medium: '#c7d2fe',   // Indigo 200 - Trung bình
    low: '#bbf7d0'       // Green 200 - Thấp
  };

  return priorityColors[priority] || priorityColors.medium;
};
