import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { ThemeProvider } from 'styled-components';
import MainApp from './components/MainApp';
import { GlobalStyles } from './styles/GlobalStyles';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 2,
    },
  },
});

// Theme for styled-components (if needed in the future)
const theme = {
  colors: {
    primary: '#2E8B57',
    secondary: '#8B4513',
    background: '#654321',
    text: '#F4E4BC',
    error: '#DC143C',
  },
  breakpoints: {
    mobile: '768px',
    tablet: '1024px',
  },
};

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <GlobalStyles />
        <MainApp />
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'rgba(45, 27, 105, 0.95)',
              color: '#ffffff',
              border: '1px solid rgba(99, 102, 241, 0.3)',
              borderRadius: '12px',
              fontSize: '14px',
              fontWeight: '500',
              fontFamily: 'Poppins, sans-serif',
              backdropFilter: 'blur(15px)',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2), 0 20px 40px rgba(0, 0, 0, 0.15)',
              padding: '16px 20px',
            },
            success: {
              iconTheme: {
                primary: '#22c55e',
                secondary: '#ffffff',
              },
              style: {
                border: '1px solid rgba(34, 197, 94, 0.3)',
                background: 'rgba(45, 27, 105, 0.95)',
              }
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#ffffff',
              },
              style: {
                border: '1px solid rgba(239, 68, 68, 0.3)',
                background: 'rgba(45, 27, 105, 0.95)',
              }
            },
          }}
        />
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;