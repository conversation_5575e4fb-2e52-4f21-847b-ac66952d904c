import axios from 'axios';
import type { StickyNote, CreateNotePayload, UpdateNotePayload, ReorderUpdate, ApiResponse, Quadrant } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for debugging
api.interceptors.request.use((config) => {
  console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
  return config;
});

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Helper function to map database response to frontend format
const mapNote = (note: any): StickyNote => ({
  id: String(note.id || note._id), // Support both SQLite (id) and MongoDB (_id)
  description: note.description || note.content || '', // Support both new and old field names
  title: note.title || '',
  color: note.color,
  quadrant: note.quadrant || mapStatusToQuadrant(note.status) || 'important_not_urgent',
  order: note.order,
  createdAt: note.createdAt,
  assigner: note.assigner || '',
  deadline: note.deadline,
  updatedAt: note.updatedAt,
  tags: Array.isArray(note.tags) ? note.tags : (typeof note.tags === 'string' ? JSON.parse(note.tags || '[]') : []),
  priority: note.priority,
  completed: Boolean(note.completed),
});

// Helper function to map old status to new quadrant
const mapStatusToQuadrant = (status: string): Quadrant => {
  switch (status) {
    case 'todo':
      return 'important_not_urgent';
    case 'processing':
      return 'important_urgent';
    case 'done':
      return 'not_important_not_urgent';
    default:
      return 'important_not_urgent';
  }
};

export const notesApi = {
  // Get all notes
  getAllNotes: async (): Promise<StickyNote[]> => {
    const response = await api.get<ApiResponse<any[]>>('/notes', {
      params: { _t: Date.now() } // Cache busting
    });
    return response.data.data.map(mapNote);
  },

  // Get notes by status
  getNotesByStatus: async (status: string): Promise<StickyNote[]> => {
    const response = await api.get<ApiResponse<any[]>>(`/notes?status=${status}`);
    return response.data.data.map(mapNote);
  },

  // Create a new note
  createNote: async (data: CreateNotePayload): Promise<StickyNote> => {
    const response = await api.post<ApiResponse<any>>('/notes', data);
    return mapNote(response.data.data);
  },

  // Update a note
  updateNote: async (id: string, data: UpdateNotePayload): Promise<StickyNote> => {
    const response = await api.put<ApiResponse<any>>(`/notes/${id}`, data);
    return mapNote(response.data.data);
  },

  // Reorder multiple notes
  reorderNotes: async (updates: ReorderUpdate[]): Promise<StickyNote[]> => {
    const response = await api.put<ApiResponse<any[]>>('/notes/reorder', { updates });
    return response.data.data.map(mapNote);
  },

  // Delete a note
  deleteNote: async (id: string): Promise<void> => {
    await api.delete(`/notes/${id}`);
  },

  // Health check
  healthCheck: async (): Promise<any> => {
    const response = await api.get('/health');
    return response.data;
  }
};

export default api;