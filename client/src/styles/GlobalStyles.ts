import styled, { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: linear-gradient(135deg, #1a1b3e 0%, #2d1b69 50%, #4c1d95 100%);
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    font-weight: 400;
    letter-spacing: 0.01em;
    padding: 0 0 10px 0; /* Add 10px bottom padding to create visible spacing */
    box-sizing: border-box; /* Ensure padding is included in height calculation */
    margin: 0;
  }

  html, body, #root {
    height: 100%;
  }

  button {
    cursor: pointer;
    border: none;
    outline: none;
    font-family: inherit;
  }

  input, textarea {
    font-family: inherit;
    outline: none;
  }
`;

export const BoardContainer = styled.div`
  height: calc(100vh - 10px); /* Reduce height to create bottom spacing */
  width: 100vw; /* Ensure full width */
  max-width: 100vw; /* Prevent overflow */
  background: linear-gradient(135deg, #1a1b3e 0%, #2d1b69 50%, #4c1d95 100%);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.3) 0%, transparent 50%);
  position: relative;
  overflow: hidden;
  margin-bottom: 10px; /* Add bottom margin to create visible spacing from screen edge */
  box-sizing: border-box;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.15) 0%, transparent 25%),
      radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.15) 0%, transparent 25%);
    pointer-events: none;
  }
`;

export const Header = styled.div`
  background: rgba(26, 27, 62, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(99, 102, 241, 0.4);
  padding: 1rem 2rem 0.75rem 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

  @media (max-width: 899px) {
    padding: 1rem 1.5rem 0.75rem 1.5rem;
  }

  @media (max-width: 599px) {
    padding: 1rem 1rem 0.75rem 1rem;
  }
  text-align: center;
  flex-shrink: 0;
  
  h1 {
    color: #f8fafc;
    font-size: 2.25rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #22d3ee 0%, #a855f7 50%, #f59e0b 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.02em;
    margin: 0;
  }
  
  p {
    color: #e0e7ff;
    font-size: 0.9rem;
    font-weight: 400;
    margin-top: 0.25rem;
    font-family: 'Poppins', sans-serif;
  }
  
  @media (max-width: 899px) {
    padding: 0.75rem 1.5rem 0.5rem 1.5rem;
    
    h1 {
      font-size: 1.9rem;
    }
    
    p {
      font-size: 0.85rem;
    }
  }
  
  @media (max-width: 599px) {
    padding: 0.5rem 1rem 0.5rem 1rem;
    
    h1 {
      font-size: 1.6rem;
    }
    
    p {
      font-size: 0.8rem;
      margin-top: 0.2rem;
    }
  }
`;

export const BoardContent = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;
  padding: 1rem 2rem 3rem 2rem;

  @media (max-width: 1199px) {
    gap: 1.5rem;
    padding: 1rem 1.5rem 2rem 1.5rem;
  }

  @media (max-width: 899px) {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 1rem 2rem 1rem;
  }
  height: calc(100vh - 120px);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  
  /* Desktop: Show all 3 columns side by side */
  @media (min-width: 1200px) {
    gap: 2.5rem;
    padding: 1.25rem 3rem 3rem 3rem;
  }
  
  /* Tablet landscape: 3 columns with smaller gap */
  @media (max-width: 1199px) and (min-width: 900px) {
    gap: 1.5rem;
    padding: 1rem 1.5rem 2.5rem 1.5rem;
  }
  
  /* Tablet portrait: 2 columns + horizontal scroll for 3rd */
  @media (max-width: 899px) and (min-width: 600px) {
    justify-content: flex-start;
    overflow-x: auto;
    overflow-y: auto;
    gap: 1.5rem;
    padding: 0.75rem 1.5rem 2.5rem 1.5rem;
    
    /* Enable horizontal scrolling */
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(15, 23, 42, 0.3);
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(148, 163, 184, 0.4);
      border-radius: 4px;
      
      &:hover {
        background: rgba(148, 163, 184, 0.6);
      }
    }
  }
  
  /* Mobile: Single column with vertical scroll between columns */
  @media (max-width: 599px) {
    flex-direction: column;
    gap: 1rem;
    padding: 0.75rem 1rem 2rem 1rem;
    overflow-y: auto;
    overflow-x: hidden;
  }
`;

export const AddNoteButtonContainer = styled.div`
  padding: 0.75rem 2rem 0 2rem;
  text-align: center;
  
  @media (max-width: 899px) {
    padding: 0.5rem 1.5rem 0 1.5rem;
  }
  
  @media (max-width: 599px) {
    padding: 0.5rem 1rem 0 1rem;
  }
`;

export const AddNoteButton = styled.button`
  background: linear-gradient(135deg, #06b6d4 0%, #8b5cf6 100%);
  color: white;
  padding: 14px 28px;
  border-radius: 16px;
  font-weight: 500;
  font-size: 1rem;
  font-family: 'Poppins', sans-serif;
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0 auto 2rem auto;
  display: block;
  min-width: 200px;
  
  &:hover {
    background: linear-gradient(135deg, #0891b2 0%, #7c3aed 100%);
    box-shadow: 0 12px 30px rgba(6, 182, 212, 0.5);
    transform: translateY(-3px) scale(1.02);
  }
  
  &:active {
    transform: translateY(-1px) scale(1.01);
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.4);
  }
`;

