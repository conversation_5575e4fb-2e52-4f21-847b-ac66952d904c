{"name": "sticky-note-kanban", "version": "1.0.0", "description": "A Sticky Note Kanban application with drag and drop functionality", "main": "electron-main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/index.js", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "node server/index.js", "install-deps": "npm install && cd client && npm install", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:stop": "docker-compose -f docker-compose.dev.yml down", "docker:build": "docker-compose -f docker-compose.dev.yml build --no-cache", "docker:logs": "docker-compose -f docker-compose.dev.yml logs -f", "docker:clean": "docker-compose -f docker-compose.dev.yml down -v && docker system prune -a --volumes -f", "electron": "NODE_ENV=development electron .", "electron-dev": "concurrently \"npm run server\" \"npm run client\" \"wait-on http://localhost:5173 && electron .\"", "electron-build": "npm run build && electron-builder", "electron-dist": "npm run build && electron-builder --publish=never"}, "keywords": ["kanban", "sticky-notes", "drag-drop", "react", "node.js"], "author": "Your Name", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "sqlite3": "^5.1.6", "morgan": "^1.10.0"}, "devDependencies": {"concurrently": "^8.2.0", "electron": "^37.4.0", "electron-builder": "^26.0.12", "nodemon": "^3.0.1", "wait-on": "^8.0.4"}, "build": {"appId": "com.stickynote.kanban", "productName": "StickyNote Kanban", "directories": {"output": "dist-electron"}, "files": ["electron-main.js", "server/**/*", "client/dist/**/*", "package.json", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns", "target": [{"target": "dmg", "arch": ["universal"]}, {"target": "zip", "arch": ["universal"]}]}, "dmg": {"title": "StickyNote Kanban", "backgroundColor": "#ffffff"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}