const { app, BrowserWindow, Menu, shell } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

// Keep a global reference of the window object
let mainWindow;
let serverProcess;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      // Enhanced performance optimizations for macOS
      experimentalFeatures: true,
      enableBlinkFeatures: 'CSSPaintAPI',
      backgroundThrottling: false,
      offscreen: false,
      // Hardware acceleration
      hardwareAcceleration: true,
      // Disable node integration for security and performance
      sandbox: false,
      // V8 cache options for faster startup
      v8CacheOptions: 'code',
      // Enable shared array buffer for better performance
      enableSharedArrayBuffer: true
    },
    titleBarStyle: 'hiddenInset', // macOS style
    icon: path.join(__dirname, 'assets', 'icon.png'),
    show: false, // Don't show until ready
    // Performance settings optimized for macOS
    transparent: false,
    frame: true,
    hasShadow: true,
    acceptFirstMouse: true,
    // macOS specific optimizations
    vibrancy: null,
    visualEffectState: 'inactive',
    // Memory and rendering optimizations
    paintWhenInitiallyHidden: false,
    // Enable GPU acceleration
    enableLargerThanScreen: false,
    // Traffic light positioning for macOS
    trafficLightPosition: { x: 20, y: 20 }
  });

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Performance optimizations after window is ready
    mainWindow.webContents.executeJavaScript(`
      // Disable drag image for better performance
      document.addEventListener('dragstart', function(e) {
        e.dataTransfer.setDragImage(new Image(), 0, 0);
      });
      
      // Optimize CSS animations
      const style = document.createElement('style');
      style.textContent = \`
        * {
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
        }
        
        [draggable="true"] {
          will-change: transform;
          transform: translate3d(0,0,0);
        }
        
        /* Improve drag performance */
        .drag-overlay {
          will-change: transform, opacity;
          backface-visibility: hidden;
          perspective: 1000;
        }
      \`;
      document.head.appendChild(style);
    `);
  });

  // Load the app
  if (isDev) {
    // Development: load from Vite dev server
    mainWindow.loadURL('http://localhost:5173');
  } else {
    // Production: load from built files
    mainWindow.loadFile(path.join(__dirname, 'client/dist/index.html'));
  }
  
  // NEVER open dev tools automatically
  // Can be opened manually via View menu if needed

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Emitted when the window is closed
  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

function startServer() {
  if (isDev) {
    // In development, assume server is running separately
    return;
  }

  // In production, start the backend server
  const { spawn } = require('child_process');
  serverProcess = spawn('node', [path.join(__dirname, 'server/index.js')], {
    env: {
      ...process.env,
      PORT: process.env.PORT || 5000,
      NODE_ENV: 'production'
    },
    stdio: 'inherit'
  });

  serverProcess.on('error', (error) => {
    console.error('Failed to start server:', error);
  });

  serverProcess.on('close', (code) => {
    console.log(`Server process exited with code ${code}`);
  });
}

// App event handlers
app.whenReady().then(() => {
  startServer();
  
  // Wait a bit for server to start in production
  if (!isDev) {
    setTimeout(() => {
      createWindow();
    }, 3000);
  } else {
    createWindow();
  }

  // Create application menu
  createMenu();

  app.on('activate', function () {
    // On macOS it's common to re-create a window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed
app.on('window-all-closed', function () {
  // Kill server process if it exists
  if (serverProcess) {
    serverProcess.kill();
  }
  
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') app.quit();
});

app.on('before-quit', () => {
  // Kill server process before quitting
  if (serverProcess) {
    serverProcess.kill();
  }
});

function createMenu() {
  const template = [
    {
      label: 'StickyNote Kanban',
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideothers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
        { type: 'separator' },
        { role: 'front' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}