#!/bin/bash

# Docker Development Script for Sticky Note Kanban

case "$1" in
  "start")
    echo "🚀 Starting Sticky Note Kanban in development mode..."
    docker-compose -f docker-compose.dev.yml up -d
    echo "✅ Application started!"
    echo "📋 Frontend: http://localhost:5173"
    echo "🔧 Backend API: http://localhost:5000"
    echo "🍃 MongoDB: mongodb://localhost:27017"
    ;;
    
  "stop")
    echo "🛑 Stopping Sticky Note Kanban..."
    docker-compose -f docker-compose.dev.yml down
    echo "✅ Application stopped!"
    ;;
    
  "restart")
    echo "🔄 Restarting Sticky Note Kanban..."
    docker-compose -f docker-compose.dev.yml down
    docker-compose -f docker-compose.dev.yml up -d
    echo "✅ Application restarted!"
    ;;
    
  "build")
    echo "🔨 Building Sticky Note Kanban images..."
    docker-compose -f docker-compose.dev.yml build --no-cache
    echo "✅ Build completed!"
    ;;
    
  "logs")
    echo "📜 Showing logs..."
    docker-compose -f docker-compose.dev.yml logs -f
    ;;
    
  "logs-backend")
    echo "📜 Showing backend logs..."
    docker-compose -f docker-compose.dev.yml logs -f backend
    ;;
    
  "logs-frontend")
    echo "📜 Showing frontend logs..."
    docker-compose -f docker-compose.dev.yml logs -f frontend
    ;;
    
  "logs-db")
    echo "📜 Showing database logs..."
    docker-compose -f docker-compose.dev.yml logs -f mongodb
    ;;
    
  "clean")
    echo "🧹 Cleaning up Docker resources..."
    docker-compose -f docker-compose.dev.yml down -v
    docker system prune -a --volumes -f
    echo "✅ Cleanup completed!"
    ;;
    
  "status")
    echo "📊 Checking application status..."
    docker-compose -f docker-compose.dev.yml ps
    ;;
    
  "shell-backend")
    echo "🐚 Opening backend shell..."
    docker-compose -f docker-compose.dev.yml exec backend sh
    ;;
    
  "shell-frontend")
    echo "🐚 Opening frontend shell..."
    docker-compose -f docker-compose.dev.yml exec frontend sh
    ;;
    
  "shell-db")
    echo "🐚 Opening MongoDB shell..."
    docker-compose -f docker-compose.dev.yml exec mongodb mongosh sticky-note-kanban
    ;;
    
  *)
    echo "🤔 Sticky Note Kanban Docker Development Script"
    echo ""
    echo "Usage: $0 {command}"
    echo ""
    echo "Commands:"
    echo "  start          Start the application in development mode"
    echo "  stop           Stop the application"
    echo "  restart        Restart the application"
    echo "  build          Build Docker images"
    echo "  logs           Show all logs"
    echo "  logs-backend   Show backend logs only"
    echo "  logs-frontend  Show frontend logs only"
    echo "  logs-db        Show database logs only"
    echo "  status         Show container status"
    echo "  shell-backend  Open backend container shell"
    echo "  shell-frontend Open frontend container shell"
    echo "  shell-db       Open MongoDB shell"
    echo "  clean          Clean up Docker resources"
    echo ""
    echo "Examples:"
    echo "  $0 start       # Start development environment"
    echo "  $0 logs        # Follow all logs"
    echo "  $0 clean       # Clean up everything"
    ;;
esac