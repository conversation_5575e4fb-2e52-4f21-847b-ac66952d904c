{"permissions": {"allow": ["Bash(npm create:*)", "Bash(npm install)", "Bash(npm run server:*)", "Bash(brew services start:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(npm run docker:build:*)", "Bash(npm run:*)", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "Bash(./test-api.sh:*)", "Bash(find:*)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(npx vite:*)", "Bash(grep:*)", "Bash(timeout 10 npm run server)", "Bash(npx tsc:*)"], "deny": [], "ask": []}}