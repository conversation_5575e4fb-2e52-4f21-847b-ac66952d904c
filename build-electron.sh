#!/bin/bash

# Build script for Electron app
set -e

echo "🔧 Building StickyNote Kanban Electron App..."

# Stop any running Docker containers
echo "⏹️  Stopping Docker containers..."
docker-compose -f docker-compose.dev.yml down || true

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build client
echo "🎨 Building React frontend..."
cd client
npm install
npm run build
cd ..

# Create production MongoDB connection for standalone app
echo "🔧 Configuring for standalone app..."
if [ ! -f .env ]; then
    echo "Creating .env file for production..."
    cat > .env << EOF
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/sticky-note-kanban
EOF
fi

# Build Electron app
echo "📱 Building Electron app..."
npm run electron-dist

echo "✅ Build completed! Check dist-electron/ folder for the app."
echo "🚀 To run in development mode: npm run electron"
echo "📦 Installation files are in: dist-electron/"