#!/bin/bash

# Docker Development Script for Sticky Note Kanban

case "$1" in
  "start")
    echo "🚀 Starting Sticky Note Kanban in development mode..."
    docker-compose -f docker-compose.dev.yml up -d
    echo "✅ Application started!"
    echo "📋 Frontend: http://localhost:5173"
    echo "🔧 Backend API: http://localhost:5000"
    echo "🍃 MongoDB: mongodb://localhost:27017"
    ;;
    
  "stop")
    echo "🛑 Stopping Sticky Note Kanban..."
    docker-compose -f docker-compose.dev.yml down
    echo "✅ Application stopped!"
    ;;
    
  "restart")
    echo "🔄 Restarting Sticky Note Kanban..."
    docker-compose -f docker-compose.dev.yml down
    docker-compose -f docker-compose.dev.yml up -d
    echo "✅ Application restarted!"
    ;;
    
  "build")
    echo "🔨 Building Sticky Note Kanban images..."
    docker-compose -f docker-compose.dev.yml build --no-cache
    echo "✅ Build completed!"
    ;;
    
  "logs")
    echo "📜 Showing logs..."
    docker-compose -f docker-compose.dev.yml logs -f
    ;;
    
  "clean")
    echo "🧹 Cleaning up Docker resources..."
    docker-compose -f docker-compose.dev.yml down -v
    docker system prune -a --volumes -f
    echo "✅ Cleanup completed!"
    ;;
    
  "status")
    echo "📊 Checking application status..."
    docker-compose -f docker-compose.dev.yml ps
    ;;
    
  *)
    echo "🤔 Sticky Note Kanban Docker Development Script"
    echo ""
    echo "Usage: $0 {command}"
    echo ""
    echo "Commands:"
    echo "  start     Start the application"
    echo "  stop      Stop the application"
    echo "  restart   Restart the application"
    echo "  build     Build Docker images"
    echo "  logs      Show all logs"
    echo "  status    Show container status"
    echo "  clean     Clean up Docker resources"
    ;;
esac