const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database file path - will be created in the app directory
const DB_PATH = path.join(__dirname, '..', 'data', 'stickynotes.db');

// Ensure data directory exists
const fs = require('fs');
const dataDir = path.dirname(DB_PATH);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

class Database {
  constructor() {
    this.db = null;
  }

  // Initialize database connection
  async connect() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(DB_PATH, (err) => {
        if (err) {
          console.error('Error opening database:', err.message);
          reject(err);
        } else {
          console.log('Connected to SQLite database');
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  // Create tables if they don't exist
  async createTables() {
    // First, check if we need to migrate from old schema
    await this.migrateSchema();

    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS sticky_notes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        description TEXT NOT NULL,
        title TEXT DEFAULT '',
        color TEXT DEFAULT '#FFEB9C',
        quadrant TEXT DEFAULT 'important_urgent' CHECK (quadrant IN ('important_urgent', 'important_not_urgent', 'not_important_urgent', 'not_important_not_urgent')),
        order_index INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        assigner TEXT DEFAULT '',
        deadline DATETIME,
        priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
        tags TEXT DEFAULT '[]',
        completed BOOLEAN DEFAULT 0
      )
    `;

    const createIndexSQL = [
      'CREATE INDEX IF NOT EXISTS idx_quadrant_priority_order ON sticky_notes (quadrant, priority, order_index)',
      'CREATE INDEX IF NOT EXISTS idx_created_at ON sticky_notes (created_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_priority ON sticky_notes (priority)',
      'CREATE INDEX IF NOT EXISTS idx_quadrant ON sticky_notes (quadrant)'
    ];

    return new Promise((resolve, reject) => {
      this.db.run(createTableSQL, (err) => {
        if (err) {
          console.error('Error creating table:', err.message);
          reject(err);
        } else {
          console.log('Tables created successfully');

          // Create indexes
          const indexPromises = createIndexSQL.map(sql =>
            new Promise((res, rej) => {
              this.db.run(sql, (err) => {
                if (err) rej(err);
                else res();
              });
            })
          );

          Promise.all(indexPromises)
            .then(() => {
              console.log('Indexes created successfully');
              resolve();
            })
            .catch(reject);
        }
      });
    });
  }

  // Migrate from old schema to new Eisenhower Matrix schema
  async migrateSchema() {
    return new Promise((resolve, reject) => {
      // Check if old schema exists
      this.db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='sticky_notes'", (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        if (!row) {
          // Table doesn't exist yet, no migration needed
          resolve();
          return;
        }

        // Get all column info to check if migration is needed
        this.db.all("PRAGMA table_info(sticky_notes)", (err, columns) => {
          if (err) {
            reject(err);
            return;
          }

          const hasContentColumn = columns.some(col => col.name === 'content');
          const hasStatusColumn = columns.some(col => col.name === 'status');
          const hasDescriptionColumn = columns.some(col => col.name === 'description');
          const hasQuadrantColumn = columns.some(col => col.name === 'quadrant');
          const hasCompletedColumn = columns.some(col => col.name === 'completed');

          if (hasContentColumn && hasStatusColumn && !hasDescriptionColumn && !hasQuadrantColumn) {
            // Need to migrate from old schema
            console.log('Migrating database schema to Eisenhower Matrix...');
            this.performMigration().then(resolve).catch(reject);
          } else if (hasDescriptionColumn && hasQuadrantColumn && !hasCompletedColumn) {
            // Need to add completion column
            console.log('Adding completion column to database...');
            this.addCompletionColumn().then(resolve).catch(reject);
          } else {
            resolve();
          }
        });
      });
    });
  }

  // Perform the actual migration
  async performMigration() {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        this.db.run('BEGIN TRANSACTION');

        // Create new table with updated schema
        const createNewTableSQL = `
          CREATE TABLE sticky_notes_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            description TEXT NOT NULL,
            title TEXT DEFAULT '',
            color TEXT DEFAULT '#FFEB9C',
            quadrant TEXT DEFAULT 'important_urgent' CHECK (quadrant IN ('important_urgent', 'important_not_urgent', 'not_important_urgent', 'not_important_not_urgent')),
            order_index INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            assigner TEXT DEFAULT '',
            deadline DATETIME,
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
            tags TEXT DEFAULT '[]',
            completed BOOLEAN DEFAULT 0
          )
        `;

        this.db.run(createNewTableSQL, (err) => {
          if (err) {
            this.db.run('ROLLBACK');
            reject(err);
            return;
          }

          // Migrate data with status to quadrant mapping
          const migrateDataSQL = `
            INSERT INTO sticky_notes_new (
              id, description, title, color, quadrant, order_index,
              created_at, updated_at, assigner, deadline, priority, tags, completed
            )
            SELECT
              id,
              content as description,
              COALESCE(title, '') as title,
              color,
              CASE
                WHEN status = 'todo' THEN 'important_not_urgent'
                WHEN status = 'processing' THEN 'important_urgent'
                WHEN status = 'done' THEN 'not_important_not_urgent'
                ELSE 'important_not_urgent'
              END as quadrant,
              order_index,
              created_at,
              updated_at,
              COALESCE(assigner, '') as assigner,
              deadline,
              COALESCE(priority, 'medium') as priority,
              '[]' as tags,
              CASE WHEN status = 'done' THEN 1 ELSE 0 END as completed
            FROM sticky_notes
          `;

          this.db.run(migrateDataSQL, (err) => {
            if (err) {
              this.db.run('ROLLBACK');
              reject(err);
              return;
            }

            // Drop old table and rename new one
            this.db.run('DROP TABLE sticky_notes', (err) => {
              if (err) {
                this.db.run('ROLLBACK');
                reject(err);
                return;
              }

              this.db.run('ALTER TABLE sticky_notes_new RENAME TO sticky_notes', (err) => {
                if (err) {
                  this.db.run('ROLLBACK');
                  reject(err);
                  return;
                }

                this.db.run('COMMIT', (err) => {
                  if (err) {
                    reject(err);
                  } else {
                    console.log('Database migration completed successfully');
                    resolve();
                  }
                });
              });
            });
          });
        });
      });
    });
  }

  // Add completion column to existing database
  async addCompletionColumn() {
    return new Promise((resolve, reject) => {
      this.db.run('BEGIN TRANSACTION', (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Add the completion column
        const addColumnSQL = `ALTER TABLE sticky_notes ADD COLUMN completed BOOLEAN DEFAULT 0`;

        this.db.run(addColumnSQL, (err) => {
          if (err) {
            this.db.run('ROLLBACK');
            reject(err);
            return;
          }

          this.db.run('COMMIT', (err) => {
            if (err) {
              reject(err);
            } else {
              console.log('Successfully added completion column');
              resolve();
            }
          });
        });
      });
    });
  }

  // Get all notes with priority sorting
  async getAllNotes() {
    const sql = `
      SELECT
        id,
        description,
        title,
        color,
        quadrant,
        order_index as 'order',
        created_at as createdAt,
        updated_at as updatedAt,
        assigner,
        deadline,
        priority,
        tags,
        completed
      FROM sticky_notes
      ORDER BY
        CASE priority
          WHEN 'urgent' THEN 0
          WHEN 'high' THEN 1
          WHEN 'medium' THEN 2
          WHEN 'low' THEN 3
          ELSE 2
        END,
        order_index ASC,
        created_at DESC
    `;

    return new Promise((resolve, reject) => {
      this.db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          // Convert SQLite 0/1 to boolean for completed field
          const processedRows = rows.map(row => ({
            ...row,
            completed: Boolean(row.completed),
            tags: JSON.parse(row.tags || '[]')
          }));
          resolve(processedRows);
        }
      });
    });
  }

  // Create a new note
  async createNote(noteData) {
    const sql = `
      INSERT INTO sticky_notes (description, title, color, quadrant, order_index, assigner, deadline, priority, tags, completed)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      noteData.description || noteData.content || '', // Support both old and new field names
      noteData.title || '',
      noteData.color || '#FFEB9C',
      noteData.quadrant || noteData.status || 'important_not_urgent', // Map old status to quadrant
      noteData.order || 0,
      noteData.assigner || '',
      noteData.deadline || null,
      noteData.priority || 'medium',
      JSON.stringify(noteData.tags || []),
      noteData.completed ? 1 : 0
    ];

    return new Promise((resolve, reject) => {
      const db = this.db; // Store reference to avoid context issues
      db.run(sql, params, function (err) {
        if (err) {
          reject(err);
        } else {
          // Get the created note
          const selectSQL = `
            SELECT
              id,
              description,
              title,
              color,
              quadrant,
              order_index as 'order',
              created_at as createdAt,
              updated_at as updatedAt,
              assigner,
              deadline,
              priority,
              tags,
              completed
            FROM sticky_notes
            WHERE id = ?
          `;

          const insertedId = this.lastID;
          db.get(selectSQL, [insertedId], (err, row) => {
            if (err) {
              reject(err);
            } else {
              // Convert SQLite 0/1 to boolean for completed field
              const processedRow = {
                ...row,
                completed: Boolean(row.completed),
                tags: JSON.parse(row.tags || '[]')
              };
              resolve(processedRow);
            }
          });
        }
      });
    });
  }

  // Update a note
  async updateNote(id, updateData) {
    const fields = [];
    const params = [];

    // Build dynamic update query with field mapping
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        if (key === 'order') {
          fields.push('order_index = ?');
          params.push(updateData[key]);
        } else if (key === 'content') {
          // Map old 'content' field to new 'description' field
          fields.push('description = ?');
          params.push(updateData[key]);
        } else if (key === 'status') {
          // Map old 'status' field to new 'quadrant' field
          fields.push('quadrant = ?');
          params.push(updateData[key]);
        } else if (key === 'tags') {
          fields.push('tags = ?');
          params.push(JSON.stringify(updateData[key]));
        } else if (key === 'completed') {
          fields.push('completed = ?');
          params.push(updateData[key] ? 1 : 0);
        } else {
          fields.push(`${key} = ?`);
          params.push(updateData[key]);
        }
      }
    });

    if (fields.length === 0) {
      throw new Error('No fields to update');
    }

    // Always update the updated_at field
    fields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    const sql = `UPDATE sticky_notes SET ${fields.join(', ')} WHERE id = ?`;

    return new Promise((resolve, reject) => {
      const db = this.db; // Store reference to avoid context issues
      db.run(sql, params, function (err) {
        if (err) {
          reject(err);
        } else if (this.changes === 0) {
          reject(new Error('Note not found'));
        } else {
          // Get the updated note
          const selectSQL = `
            SELECT
              id,
              description,
              title,
              color,
              quadrant,
              order_index as 'order',
              created_at as createdAt,
              updated_at as updatedAt,
              assigner,
              deadline,
              priority,
              tags,
              completed
            FROM sticky_notes
            WHERE id = ?
          `;

          db.get(selectSQL, [id], (err, row) => {
            if (err) {
              reject(err);
            } else {
              // Convert SQLite 0/1 to boolean for completed field
              const processedRow = {
                ...row,
                completed: Boolean(row.completed),
                tags: JSON.parse(row.tags || '[]')
              };
              resolve(processedRow);
            }
          });
        }
      });
    });
  }

  // Delete a note
  async deleteNote(id) {
    const sql = 'DELETE FROM sticky_notes WHERE id = ?';

    return new Promise((resolve, reject) => {
      this.db.run(sql, [id], function (err) {
        if (err) {
          reject(err);
        } else if (this.changes === 0) {
          reject(new Error('Note not found'));
        } else {
          resolve({ deleted: true, id: id });
        }
      });
    });
  }

  // Update multiple notes (for reordering)
  async updateMultipleNotes(updates) {
    return new Promise((resolve, reject) => {
      const db = this.db; // Store reference to avoid context issues
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const promises = updates.map(update => {
          return new Promise((res, rej) => {
            const fields = [];
            const params = [];

            Object.keys(update).forEach(key => {
              if (key !== 'id' && update[key] !== undefined) {
                if (key === 'order') {
                  fields.push('order_index = ?');
                  params.push(update[key]);
                } else if (key === 'content') {
                  // Map old 'content' field to new 'description' field
                  fields.push('description = ?');
                  params.push(update[key]);
                } else if (key === 'status') {
                  // Map old 'status' field to new 'quadrant' field
                  fields.push('quadrant = ?');
                  params.push(update[key]);
                } else if (key === 'tags') {
                  fields.push('tags = ?');
                  params.push(JSON.stringify(update[key]));
                } else {
                  fields.push(`${key} = ?`);
                  params.push(update[key]);
                }
              }
            });

            fields.push('updated_at = CURRENT_TIMESTAMP');
            params.push(update.id);

            const sql = `UPDATE sticky_notes SET ${fields.join(', ')} WHERE id = ?`;

            db.run(sql, params, function (err) {
              if (err) {
                rej(err);
              } else {
                res();
              }
            });
          });
        });

        Promise.all(promises)
          .then(() => {
            db.run('COMMIT', (err) => {
              if (err) {
                reject(err);
              } else {
                // Fetch updated notes
                const ids = updates.map(u => u.id).join(',');
                const sql = `SELECT * FROM sticky_notes WHERE id IN (${ids}) ORDER BY order_index ASC`;
                db.all(sql, (err, rows) => {
                  if (err) {
                    reject(err);
                  } else {
                    const mappedRows = rows.map(row => ({
                      id: row.id,
                      description: row.description,
                      title: row.title || '',
                      color: row.color,
                      quadrant: row.quadrant,
                      order: row.order_index,
                      createdAt: row.created_at,
                      updatedAt: row.updated_at,
                      assigner: row.assigner || '',
                      deadline: row.deadline,
                      priority: row.priority || 'medium',
                      tags: JSON.parse(row.tags || '[]')
                    }));
                    resolve(mappedRows);
                  }
                });
              }
            });
          })
          .catch((err) => {
            db.run('ROLLBACK');
            reject(err);
          });
      });
    });
  }

  // Close database connection
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('Error closing database:', err.message);
        } else {
          console.log('Database connection closed');
        }
      });
    }
  }
}

// Create singleton instance
const database = new Database();

module.exports = database;
