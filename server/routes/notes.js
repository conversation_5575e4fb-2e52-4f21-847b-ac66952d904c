const express = require('express');
const Joi = require('joi');
const database = require('../database/sqlite');

const router = express.Router();


// Validation schemas
const createNoteSchema = Joi.object({
  description: Joi.string().max(500).trim().default(''),
  content: Joi.string().max(500).trim().optional(), // For backward compatibility
  title: Joi.string().max(300).trim().default(''), // Increased to 300 characters
  color: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/).default('#FFEB9C'),
  quadrant: Joi.string().valid('important_urgent', 'important_not_urgent', 'not_important_urgent', 'not_important_not_urgent').default('important_not_urgent'),
  status: Joi.string().valid('todo', 'processing', 'done').optional(), // For backward compatibility
  assigner: Joi.string().max(100).trim().allow('').default(''),
  deadline: Joi.date().min('now').allow(null).default(null),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent').default('medium'),
  tags: Joi.array().items(Joi.string()).default([]),
  completed: Joi.boolean().default(false)
});

const updateNoteSchema = Joi.object({
  description: Joi.string().max(500).trim(),
  content: Joi.string().max(500).trim(), // For backward compatibility
  title: Joi.string().max(300).trim(), // Increased to 300 characters
  color: Joi.string().pattern(/^#[0-9A-Fa-f]{6}$/),
  quadrant: Joi.string().valid('important_urgent', 'important_not_urgent', 'not_important_urgent', 'not_important_not_urgent'),
  status: Joi.string().valid('todo', 'processing', 'done'), // For backward compatibility
  order: Joi.number().min(0),
  assigner: Joi.string().max(100).trim().allow(''),
  deadline: Joi.date().min('now').allow(null),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent'),
  tags: Joi.array().items(Joi.string()),
  completed: Joi.boolean()
}).min(1);

const reorderSchema = Joi.object({
  updates: Joi.array().items(
    Joi.object({
      id: Joi.string().required(),
      quadrant: Joi.string().valid('important_urgent', 'important_not_urgent', 'not_important_urgent', 'not_important_not_urgent'),
      status: Joi.string().valid('todo', 'processing', 'done'), // For backward compatibility
      order: Joi.number().min(0)
    }).min(1)
  ).min(1).required()
});

// GET /api/notes - Get all notes
router.get('/', async (req, res) => {
  try {
    const notes = await database.getAllNotes();

    res.json({
      success: true,
      data: notes,
      count: notes.length
    });
  } catch (error) {
    console.error('Error fetching notes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notes',
      error: error.message
    });
  }
});

// POST /api/notes - Create a new note
router.post('/', async (req, res) => {
  try {
    const { error, value } = createNoteSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details.map(detail => detail.message)
      });
    }

    // Handle field mapping for backward compatibility
    const noteData = { ...value };

    // Map content to description if description is empty
    if (!noteData.description && noteData.content) {
      noteData.description = noteData.content;
    }

    // Map status to quadrant if quadrant is not provided
    if (!noteData.quadrant && noteData.status) {
      switch (noteData.status) {
        case 'todo':
          noteData.quadrant = 'important_not_urgent';
          break;
        case 'processing':
          noteData.quadrant = 'important_urgent';
          break;
        case 'done':
          noteData.quadrant = 'not_important_not_urgent';
          break;
        default:
          noteData.quadrant = 'important_not_urgent';
      }
    }

    const note = await database.createNote(noteData);

    res.status(201).json({
      success: true,
      data: note,
      message: 'Note created successfully'
    });
  } catch (error) {
    console.error('Error creating note:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create note',
      error: error.message
    });
  }
});

// PUT /api/notes/reorder - Reorder multiple notes
router.put('/reorder', async (req, res) => {
  try {
    const { error, value } = reorderSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details.map(detail => detail.message)
      });
    }

    const { updates } = value;

    // Handle field mapping for backward compatibility
    const mappedUpdates = updates.map(update => {
      const mappedUpdate = { ...update };

      // Map status to quadrant if quadrant is not provided
      if (!mappedUpdate.quadrant && mappedUpdate.status) {
        switch (mappedUpdate.status) {
          case 'todo':
            mappedUpdate.quadrant = 'important_not_urgent';
            break;
          case 'processing':
            mappedUpdate.quadrant = 'important_urgent';
            break;
          case 'done':
            mappedUpdate.quadrant = 'not_important_not_urgent';
            break;
          default:
            mappedUpdate.quadrant = 'important_not_urgent';
        }
      }

      return mappedUpdate;
    });

    const updatedNotes = await database.updateMultipleNotes(mappedUpdates);

    res.json({
      success: true,
      data: updatedNotes,
      message: 'Notes reordered successfully'
    });
  } catch (error) {
    console.error('Error reordering notes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reorder notes',
      error: error.message
    });
  }
});

// PUT /api/notes/:id - Update a note
router.put('/:id', async (req, res) => {
  try {
    const { error, value } = updateNoteSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details.map(detail => detail.message)
      });
    }

    // Handle field mapping for backward compatibility
    const updateData = { ...value };

    // Map content to description if description is not provided
    if (!updateData.description && updateData.content) {
      updateData.description = updateData.content;
    }

    // Map status to quadrant if quadrant is not provided
    if (!updateData.quadrant && updateData.status) {
      switch (updateData.status) {
        case 'todo':
          updateData.quadrant = 'important_not_urgent';
          break;
        case 'processing':
          updateData.quadrant = 'important_urgent';
          break;
        case 'done':
          updateData.quadrant = 'not_important_not_urgent';
          break;
        default:
          updateData.quadrant = 'important_not_urgent';
      }
    }

    const note = await database.updateNote(req.params.id, updateData);

    res.json({
      success: true,
      data: note,
      message: 'Note updated successfully'
    });
  } catch (error) {
    console.error('Error updating note:', error);
    if (error.message === 'Note not found') {
      return res.status(404).json({
        success: false,
        message: 'Note not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to update note',
      error: error.message
    });
  }
});

// DELETE /api/notes/:id - Delete a note
router.delete('/:id', async (req, res) => {
  try {
    await database.deleteNote(req.params.id);

    res.json({
      success: true,
      message: 'Note deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting note:', error);
    if (error.message === 'Note not found') {
      return res.status(404).json({
        success: false,
        message: 'Note not found'
      });
    }
    res.status(500).json({
      success: false,
      message: 'Failed to delete note',
      error: error.message
    });
  }
});

module.exports = router;