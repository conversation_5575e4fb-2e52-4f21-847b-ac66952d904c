#!/bin/bash

# Desktop app launcher script
set -e

echo "🚀 Starting StickyNote Kanban Desktop App..."

# Check if Docker containers are running
if ! docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
    echo "📦 Starting backend services..."
    docker-compose -f docker-compose.dev.yml up -d
    echo "⏳ Waiting for services to be ready..."
    sleep 10
fi

# Check if frontend is accessible
if ! curl -s http://localhost:5173 > /dev/null; then
    echo "❌ Frontend not accessible. Please check if services are running."
    echo "   Try: docker-compose -f docker-compose.dev.yml logs"
    exit 1
fi

echo "✅ Services are ready!"
echo "🖥️  Launching desktop app..."

# Launch Electron app (silent mode)
NODE_ENV=production npm run electron > /dev/null 2>&1