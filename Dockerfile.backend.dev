# Development Backend Dockerfile
FROM node:20-alpine

WORKDIR /app

# Install nodemon globally for development
RUN npm install -g nodemon

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev)
RUN npm ci

# Copy application code
COPY server ./server

# Create data directory for SQLite database
RUN mkdir -p /app/server/data

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 5000

# Start the application with nodemon for development
CMD ["nodemon", "server/index.js"]