const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testAPI() {
  console.log('🧪 Testing Sticky Note Kanban API...\n');
  
  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthRes = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health:', healthRes.data);
    
    // Test 2: Get all notes (should be empty initially)
    console.log('\n2. Getting all notes...');
    const notesRes = await axios.get(`${API_BASE}/notes`);
    console.log('✅ Notes:', notesRes.data);
    
    // Test 3: Create a test note
    console.log('\n3. Creating a test note...');
    const createRes = await axios.post(`${API_BASE}/notes`, {
      content: 'Test sticky note from Docker setup! 🎉',
      color: '#FFEB9C',
      assigner: 'Docker Test',
      status: 'todo'
    });
    console.log('✅ Created note:', createRes.data);
    
    const noteId = createRes.data.data._id;
    
    // Test 4: Get notes again (should have 1 note)
    console.log('\n4. Getting notes after creation...');
    const notesRes2 = await axios.get(`${API_BASE}/notes`);
    console.log('✅ Notes after creation:', notesRes2.data);
    
    // Test 5: Update the note
    console.log('\n5. Updating the note...');
    const updateRes = await axios.put(`${API_BASE}/notes/${noteId}`, {
      content: 'Updated test note! 🚀',
      status: 'processing'
    });
    console.log('✅ Updated note:', updateRes.data);
    
    // Test 6: Delete the note
    console.log('\n6. Deleting the test note...');
    const deleteRes = await axios.delete(`${API_BASE}/notes/${noteId}`);
    console.log('✅ Deleted note:', deleteRes.data);
    
    console.log('\n🎉 All tests passed! The API is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

testAPI();