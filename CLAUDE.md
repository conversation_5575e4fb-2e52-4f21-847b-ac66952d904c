# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Sticky Note Kanban application built with React + TypeScript frontend and Node.js + Express backend with SQLite database. Users can create, edit, delete, and drag-and-drop sticky notes across Todo, Processing, and Done columns.

## Development Commands

### Quick Start
- `npm run dev` - Starts both backend server and frontend client concurrently
- `npm run install-deps` - Install dependencies for both root and client

### Backend Only
- `npm run server` - Start backend server on port 5000 with nodemon
- `npm start` - Start production backend server

### Frontend Only  
- `npm run client` - Start frontend Vite dev server (from client directory)
- `cd client && npm run dev` - Alternative way to start frontend
- `cd client && npm run build` - Build frontend for production
- `cd client && npm run lint` - Lint frontend code

### Docker Development
- `npm run docker:dev` - Start development environment with Docker Compose
- `npm run docker:stop` - Stop Docker containers
- `npm run docker:build` - Build Docker images
- `npm run docker:logs` - View Docker logs
- `npm run docker:clean` - Clean up Docker volumes and images

### Electron (Desktop App)
- `npm run electron` - Run as desktop app (development mode)
- `npm run electron-dev` - Start server, client, and electron concurrently
- `npm run electron-build` - Build Electron app for distribution

## Architecture

### Backend Structure
- `server/index.js` - Express server with CORS, Helmet, Morgan middleware
- `server/database/sqlite.js` - SQLite database connection and operations
- `server/routes/notes.js` - REST API routes for CRUD operations with Joi validation
- Database: SQLite with `sticky_notes` table containing priority-based sorting

### Frontend Structure  
- `client/src/App.tsx` - Main app with React Query, Toast notifications, Styled Components theme
- `client/src/components/Board.tsx` - Main kanban board component
- `client/src/components/Column.tsx` - Individual column (Todo/Processing/Done)
- `client/src/components/StickyNote.tsx` - Individual sticky note component
- `client/src/services/api.ts` - API client for backend communication
- Uses @dnd-kit for drag and drop, @tanstack/react-query for state management

### Key Features
- Drag and drop notes between columns using @dnd-kit
- Priority-based sorting (urgent, high, medium, low)
- Real-time updates with optimistic UI
- Modal editor for creating/editing notes
- Color picker for note customization
- Assignee and deadline fields
- Responsive design with styled-components

### Database Schema
The `sticky_notes` table includes:
- Basic fields: id, content, color, status, order_index
- Metadata: created_at, updated_at, assigner, deadline, priority, title
- Status values: 'todo', 'processing', 'done'
- Priority values: 'low', 'medium', 'high', 'urgent'

### API Endpoints
- `GET /api/notes` - Fetch all notes sorted by priority and order
- `POST /api/notes` - Create new note
- `PUT /api/notes/:id` - Update existing note
- `DELETE /api/notes/:id` - Delete note
- `PUT /api/notes/reorder` - Bulk update for drag-and-drop reordering
- `GET /api/health` - Health check endpoint

## Tech Stack
- **Backend**: Node.js, Express, SQLite3, Joi validation
- **Frontend**: React 18, TypeScript, Vite, @dnd-kit, React Query, Styled Components
- **Desktop**: Electron support for cross-platform desktop app
- **Docker**: Development environment containerization

## Testing & Quality
Run `cd client && npm run lint` for frontend linting. No test framework is currently configured.

## Development Notes
- Server runs on port 5000, client on port 5173 (Vite default)
- CORS configured for development cross-origin requests
- Database file stored at `server/data/stickynotes.db`
- Uses concurrently to run both servers simultaneously
- Supports both web and desktop (Electron) deployment
- Column collapse states are persisted in localStorage (Done column)

