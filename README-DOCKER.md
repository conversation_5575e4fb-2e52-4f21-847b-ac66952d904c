# Sticky Note Kanban - Docker Setup

## 🐳 Hướng Dẫn Chạy Với Docker

### <PERSON><PERSON><PERSON>
- Docker (phi<PERSON><PERSON> bản 20.10+)
- Docker Compose (phiên bản 2.0+)

### Cách 1: Sử dụng NPM Scripts (Khuyến nghị)

#### Chạy Ứng Dụng Development
```bash
# Khởi động toàn bộ stack (MongoDB + Backend + Frontend)
npm run docker:dev

# Xem logs của tất cả services
npm run docker:logs

# Dừng ứng dụng
npm run docker:stop

# Build lại images (khi có thay đổi dependencies)
npm run docker:build

# Xóa tất cả containers và volumes
npm run docker:clean
```

### Cách 2: Sử dụng Docker Compose Trực Tiếp

#### Development Mode
```bash
# Khởi động
docker-compose -f docker-compose.dev.yml up -d

# Xem logs
docker-compose -f docker-compose.dev.yml logs -f

# Dừng
docker-compose -f docker-compose.dev.yml down

# Build từ đầu
docker-compose -f docker-compose.dev.yml build --no-cache
```

#### Production Mode
```bash
# Khởi động production
docker-compose up -d

# Dừng production
docker-compose down
```

## 🔗 Kết Nối

Sau khi chạy thành công:
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/api/health
- **MongoDB**: mongodb://localhost:27017

## 📁 Cấu Trúc Docker

### Services
1. **mongodb**: MongoDB database
2. **backend**: Node.js Express API server
3. **frontend**: React Vite development server

### Volumes
- `mongodb_dev_data`: Lưu trữ dữ liệu MongoDB
- `backend_node_modules`: Node modules của backend
- `frontend_node_modules`: Node modules của frontend

### Networks
- `sticky-note-network`: Internal Docker network

## 🔧 Troubleshooting

### Lỗi Port đã được sử dụng
```bash
# Kiểm tra port đang được sử dụng
lsof -i :5173  # Frontend
lsof -i :5000  # Backend
lsof -i :27017 # MongoDB

# Dừng tất cả containers
docker-compose -f docker-compose.dev.yml down
```

### Lỗi Build
```bash
# Xóa cache và build lại
docker-compose -f docker-compose.dev.yml build --no-cache

# Xóa tất cả images và build từ đầu
docker system prune -a
docker-compose -f docker-compose.dev.yml build --no-cache
```

### Xem Logs Chi Tiết
```bash
# Logs tất cả services
docker-compose -f docker-compose.dev.yml logs -f

# Logs từng service
docker-compose -f docker-compose.dev.yml logs -f backend
docker-compose -f docker-compose.dev.yml logs -f frontend
docker-compose -f docker-compose.dev.yml logs -f mongodb
```

### Kết Nối Vào Container
```bash
# Backend container
docker-compose -f docker-compose.dev.yml exec backend sh

# Frontend container
docker-compose -f docker-compose.dev.yml exec frontend sh

# MongoDB shell
docker-compose -f docker-compose.dev.yml exec mongodb mongosh sticky-note-kanban
```

## 🔥 Hot Reload

Cả backend và frontend đều hỗ trợ hot reload:
- **Backend**: Sử dụng nodemon, tự động restart khi file thay đổi
- **Frontend**: Vite HMR, cập nhật real-time

## 🌍 Environment Variables

### Backend (.env)
```env
PORT=5000
MONGODB_URI=mongodb://mongodb:27017/sticky-note-kanban
NODE_ENV=development
```

### Frontend (Docker environment)
```env
VITE_API_BASE_URL=http://localhost:3001/api
```

## 📊 Monitoring

### Kiểm Tra Trạng Thái
```bash
# Container status
docker-compose -f docker-compose.dev.yml ps

# Resource usage
docker stats

# Health checks
curl http://localhost:5000/api/health
```

## 🧹 Cleanup

### Xóa Tất Cả (Cẩn Thận!)
```bash
# Dừng và xóa containers + volumes
docker-compose -f docker-compose.dev.yml down -v

# Xóa tất cả Docker resources
docker system prune -a --volumes -f

# Hoặc sử dụng script
npm run docker:clean
```

## 🚀 Production Deployment

Để deploy production, sử dụng file `docker-compose.yml`:

```bash
# Build production images
docker-compose build

# Chạy production
docker-compose up -d

# Monitoring
docker-compose logs -f
```

## 📝 Notes

- Development mode mount source code vào container để hỗ trợ hot reload
- Production mode copy code vào image để tối ưu performance
- MongoDB data được persist trong Docker volume
- Tất cả services chạy trong cùng Docker network để giao tiếp nội bộ