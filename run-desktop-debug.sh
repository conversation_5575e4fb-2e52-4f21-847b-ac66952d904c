#!/bin/bash

# Desktop app launcher script (DEBUG MODE)
set -e

echo "🚀 Starting StickyNote Kanban Desktop App (DEBUG MODE)..."

# Check if Docker containers are running
if ! docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
    echo "📦 Starting backend services..."
    docker-compose -f docker-compose.dev.yml up -d
    echo "⏳ Waiting for services to be ready..."
    sleep 10
fi

echo "✅ Services are ready!"
echo "🖥️  Launching desktop app in debug mode..."

# Launch Electron app with console output
NODE_ENV=development npm run electron