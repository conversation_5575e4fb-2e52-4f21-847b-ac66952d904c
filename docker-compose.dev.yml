version: "3.8"

services:
  # Backend API (Development) - Now using SQLite
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend.dev
    container_name: sticky-note-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
    ports:
      - "3001:5000"
    volumes:
      - ./server:/app/server
      - sqlite_data:/app/server/data # SQLite database storage
    networks:
      - sticky-note-network

  # Frontend React App (Development)
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: sticky-note-frontend-dev
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: http://localhost:3001/api
    ports:
      - "5173:5173"
    depends_on:
      - backend
    volumes:
      - ./client/src:/app/src
      - ./client/public:/app/public
      - ./client/index.html:/app/index.html
      - ./client/vite.config.ts:/app/vite.config.ts
      - ./client/tsconfig.json:/app/tsconfig.json
      - ./client/tsconfig.app.json:/app/tsconfig.app.json
      - ./client/tsconfig.node.json:/app/tsconfig.node.json
      - frontend_node_modules:/app/node_modules
    networks:
      - sticky-note-network

volumes:
  sqlite_data:
    driver: local
  backend_node_modules:
    driver: local
  frontend_node_modules:
    driver: local

networks:
  sticky-note-network:
    driver: bridge
